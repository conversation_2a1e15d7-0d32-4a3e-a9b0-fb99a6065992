/**
 * device-point-manager.js - 设备点位管理器
 * 处理设备点位的添加、编辑和管理
 */

import { showStatusMessage } from './ui-utils.js';

class DevicePointManager {
    constructor(mapController, deviceManager) {
        this.mapController = mapController;
        this.deviceManager = deviceManager;
        this.devicePoints = [];
        this.selectedDevice = null;
    }

    /**
     * 添加设备点位
     * @param {Object} position - 点击位置
     */
    addDevicePoint(position) {
        // 计算地图坐标
        const mapCoords = this.calculateMapCoordinates(position);
        
        // 创建设备数据
        const deviceData = {
            id: `device-${Date.now()}`,
            name: '新设备',
            type: 'camera',
            status: 'normal',
            x: mapCoords.x,
            y: mapCoords.y,
            size: 24,
            rotation: 0,
            description: '',
            installDate: new Date().toISOString().split('T')[0],
            model: '',
            ip: '',
            port: 80
        };

        // 显示设备配置对话框
        this.showDeviceConfigDialog(deviceData, (configuredDevice) => {
            // 添加到设备列表
            this.devicePoints.push(configuredDevice);
            
            // 在地图上显示设备
            this.renderDevicePoint(configuredDevice);
            
            // 保存到存储
            this.saveDevicePoint(configuredDevice);
            
            showStatusMessage(`已添加设备: ${configuredDevice.name}`);
        });
    }

    /**
     * 计算地图坐标
     * @param {Object} position - 屏幕位置
     * @returns {Object} 地图坐标
     */
    calculateMapCoordinates(position) {
        const zoom = this.mapController.zoom || 1;
        const panX = this.mapController.panX || 0;
        const panY = this.mapController.panY || 0;

        return {
            x: (position.x - panX) / zoom,
            y: (position.y - panY) / zoom
        };
    }

    /**
     * 显示设备配置对话框
     * @param {Object} deviceData - 设备数据
     * @param {Function} onConfirm - 确认回调
     */
    showDeviceConfigDialog(deviceData, onConfirm) {
        // 创建模态框
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay device-config-modal';
        overlay.style.cssText = `
            display: flex !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(0,0,0,0.5) !important;
            z-index: 1000 !important;
            justify-content: center !important;
            align-items: center !important;
        `;

        const modal = document.createElement('div');
        modal.className = 'device-config-content';
        modal.style.cssText = `
            background: #fff !important;
            padding: 24px !important;
            border-radius: 8px !important;
            min-width: 500px !important;
            max-width: 600px !important;
            max-height: 80vh !important;
            overflow-y: auto !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3) !important;
            position: relative !important;
        `;

        modal.innerHTML = `
            <div class="modal-header" style="margin-bottom: 20px;">
                <h2 style="margin: 0; color: #333;">设备配置</h2>
                <p style="margin: 8px 0 0 0; color: #666; font-size: 14px;">
                    配置设备的基本信息和显示参数
                </p>
            </div>
            
            <div class="modal-body">
                <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                    <div class="form-group" style="flex: 1;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">设备名称</label>
                        <input type="text" id="deviceName" value="${deviceData.name}" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                    </div>
                    <div class="form-group" style="flex: 1;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">设备类型</label>
                        <select id="deviceType" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                            <option value="camera" ${deviceData.type === 'camera' ? 'selected' : ''}>📹 摄像头</option>
                            <option value="sensor" ${deviceData.type === 'sensor' ? 'selected' : ''}>🌡️ 传感器</option>
                            <option value="alarm" ${deviceData.type === 'alarm' ? 'selected' : ''}>🚨 报警器</option>
                            <option value="detector" ${deviceData.type === 'detector' ? 'selected' : ''}>🔍 探测器</option>
                        </select>
                    </div>
                </div>

                <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                    <div class="form-group" style="flex: 1;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">设备状态</label>
                        <select id="deviceStatus" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                            <option value="normal" ${deviceData.status === 'normal' ? 'selected' : ''}>🟢 正常</option>
                            <option value="warning" ${deviceData.status === 'warning' ? 'selected' : ''}>🟡 警告</option>
                            <option value="error" ${deviceData.status === 'error' ? 'selected' : ''}>🔴 错误</option>
                            <option value="offline" ${deviceData.status === 'offline' ? 'selected' : ''}>⚫ 离线</option>
                        </select>
                    </div>
                    <div class="form-group" style="flex: 1;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">安装日期</label>
                        <input type="date" id="installDate" value="${deviceData.installDate}" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                    </div>
                </div>

                <div class="form-group" style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 4px; font-weight: 500;">设备描述</label>
                    <textarea id="deviceDescription" rows="3" placeholder="输入设备描述..." style="
                        width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                        font-size: 14px; resize: vertical; box-sizing: border-box;
                    ">${deviceData.description}</textarea>
                </div>

                <div class="form-section" style="margin-bottom: 20px;">
                    <h3 style="margin: 0 0 12px 0; font-size: 16px; color: #333;">显示设置</h3>
                    
                    <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                        <div class="form-group" style="flex: 1;">
                            <label style="display: block; margin-bottom: 4px; font-weight: 500;">图标大小</label>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <input type="range" id="deviceSize" min="16" max="48" value="${deviceData.size}" style="flex: 1;">
                                <span id="sizeValue" style="min-width: 40px; font-size: 14px;">${deviceData.size}px</span>
                            </div>
                        </div>
                        <div class="form-group" style="flex: 1;">
                            <label style="display: block; margin-bottom: 4px; font-weight: 500;">旋转角度</label>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <input type="range" id="deviceRotation" min="0" max="360" value="${deviceData.rotation}" style="flex: 1;">
                                <span id="rotationValue" style="min-width: 40px; font-size: 14px;">${deviceData.rotation}°</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3 style="margin: 0 0 12px 0; font-size: 16px; color: #333;">网络设置</h3>
                    
                    <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                        <div class="form-group" style="flex: 2;">
                            <label style="display: block; margin-bottom: 4px; font-weight: 500;">IP地址</label>
                            <input type="text" id="deviceIP" value="${deviceData.ip}" placeholder="*************" style="
                                width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                                font-size: 14px; box-sizing: border-box;
                            ">
                        </div>
                        <div class="form-group" style="flex: 1;">
                            <label style="display: block; margin-bottom: 4px; font-weight: 500;">端口</label>
                            <input type="number" id="devicePort" value="${deviceData.port}" min="1" max="65535" style="
                                width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                                font-size: 14px; box-sizing: border-box;
                            ">
                        </div>
                    </div>

                    <div class="form-group">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">设备型号</label>
                        <input type="text" id="deviceModel" value="${deviceData.model}" placeholder="输入设备型号" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                    </div>
                </div>
            </div>
            
            <div class="modal-actions" style="margin-top: 24px; text-align: right; display: flex; gap: 12px; justify-content: flex-end;">
                <button id="cancelDeviceBtn" style="
                    background: #6b7280; color: white; border: none; padding: 10px 20px;
                    border-radius: 6px; cursor: pointer; font-size: 14px;
                ">取消</button>
                <button id="confirmDeviceBtn" style="
                    background: #3b82f6; color: white; border: none; padding: 10px 20px;
                    border-radius: 6px; cursor: pointer; font-size: 14px;
                ">确认添加</button>
            </div>
        `;

        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // 绑定滑块事件
        this.bindSliderEvents(modal);

        // 绑定按钮事件
        this.bindDeviceConfigEvents(overlay, deviceData, onConfirm);
    }

    /**
     * 绑定滑块事件
     * @param {HTMLElement} modal - 模态框元素
     */
    bindSliderEvents(modal) {
        const sizeSlider = modal.querySelector('#deviceSize');
        const sizeValue = modal.querySelector('#sizeValue');
        const rotationSlider = modal.querySelector('#deviceRotation');
        const rotationValue = modal.querySelector('#rotationValue');

        sizeSlider.addEventListener('input', (e) => {
            sizeValue.textContent = `${e.target.value}px`;
        });

        rotationSlider.addEventListener('input', (e) => {
            rotationValue.textContent = `${e.target.value}°`;
        });
    }

    /**
     * 绑定设备配置事件
     * @param {HTMLElement} overlay - 覆盖层
     * @param {Object} deviceData - 设备数据
     * @param {Function} onConfirm - 确认回调
     */
    bindDeviceConfigEvents(overlay, deviceData, onConfirm) {
        const confirmBtn = overlay.querySelector('#confirmDeviceBtn');
        const cancelBtn = overlay.querySelector('#cancelDeviceBtn');

        confirmBtn.addEventListener('click', () => {
            // 收集表单数据
            const configuredDevice = {
                ...deviceData,
                name: overlay.querySelector('#deviceName').value.trim(),
                type: overlay.querySelector('#deviceType').value,
                status: overlay.querySelector('#deviceStatus').value,
                description: overlay.querySelector('#deviceDescription').value.trim(),
                installDate: overlay.querySelector('#installDate').value,
                size: parseInt(overlay.querySelector('#deviceSize').value),
                rotation: parseInt(overlay.querySelector('#deviceRotation').value),
                ip: overlay.querySelector('#deviceIP').value.trim(),
                port: parseInt(overlay.querySelector('#devicePort').value),
                model: overlay.querySelector('#deviceModel').value.trim()
            };

            // 验证必填字段
            if (!configuredDevice.name) {
                alert('请输入设备名称');
                return;
            }

            overlay.remove();
            onConfirm(configuredDevice);
        });

        cancelBtn.addEventListener('click', () => {
            overlay.remove();
        });

        // 点击背景关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
            }
        });
    }

    /**
     * 在地图上渲染设备点位
     * @param {Object} device - 设备数据
     */
    renderDevicePoint(device) {
        const mapViewport = document.getElementById('mapViewport');
        if (!mapViewport) return;

        // 创建设备元素
        const deviceElement = document.createElement('div');
        deviceElement.className = 'device-point';
        deviceElement.id = `device-${device.id}`;
        deviceElement.style.cssText = `
            position: absolute;
            left: ${device.x}px;
            top: ${device.y}px;
            width: ${device.size}px;
            height: ${device.size}px;
            transform: translate(-50%, -50%) rotate(${device.rotation}deg);
            cursor: pointer;
            z-index: 100;
            transition: all 0.2s ease;
        `;

        // 设备图标
        const deviceIcon = this.getDeviceIcon(device.type, device.status);
        deviceElement.innerHTML = `
            <div style="
                width: 100%;
                height: 100%;
                background: ${this.getStatusColor(device.status)};
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: ${device.size * 0.6}px;
                color: white;
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            ">${deviceIcon}</div>
        `;

        // 添加悬停效果
        deviceElement.addEventListener('mouseenter', () => {
            this.showDeviceTooltip(device, deviceElement);
        });

        deviceElement.addEventListener('mouseleave', () => {
            this.hideDeviceTooltip();
        });

        // 添加点击事件
        deviceElement.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectDevice(device);
        });

        mapViewport.appendChild(deviceElement);
    }

    /**
     * 获取设备图标
     * @param {string} type - 设备类型
     * @param {string} status - 设备状态
     * @returns {string} 图标字符
     */
    getDeviceIcon(type, status) {
        const icons = {
            camera: '📹',
            sensor: '🌡️',
            alarm: '🚨',
            detector: '🔍'
        };
        return icons[type] || '📹';
    }

    /**
     * 获取状态颜色
     * @param {string} status - 设备状态
     * @returns {string} 颜色值
     */
    getStatusColor(status) {
        const colors = {
            normal: '#10b981',
            warning: '#f59e0b',
            error: '#ef4444',
            offline: '#6b7280'
        };
        return colors[status] || '#10b981';
    }

    /**
     * 显示设备提示框
     * @param {Object} device - 设备数据
     * @param {HTMLElement} element - 设备元素
     */
    showDeviceTooltip(device, element) {
        // TODO: 实现设备提示框
        console.log('显示设备提示框:', device);
    }

    /**
     * 隐藏设备提示框
     */
    hideDeviceTooltip() {
        // TODO: 实现隐藏提示框
    }

    /**
     * 选择设备
     * @param {Object} device - 设备数据
     */
    selectDevice(device) {
        this.selectedDevice = device;
        showStatusMessage(`已选择设备: ${device.name}`);
        // TODO: 实现设备选择高亮效果
    }

    /**
     * 保存设备点位到存储
     * @param {Object} device - 设备数据
     */
    async saveDevicePoint(device) {
        try {
            if (this.mapController.storage) {
                await this.mapController.storage.saveDevice(device);
                console.log('设备点位保存成功:', device.id);
            }
        } catch (error) {
            console.error('保存设备点位失败:', error);
            showStatusMessage('保存设备失败', 3000);
        }
    }
}

export default DevicePointManager;
