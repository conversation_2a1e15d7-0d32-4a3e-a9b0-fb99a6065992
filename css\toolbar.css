/* toolbar.css - 工具栏样式 */

/* 顶部浮动工具栏 */
.map-toolbar {
    position: absolute;
    top: 16px;
    left: 16px;
    right: 16px;
    display: flex;
    align-items: center;
    /* justify-content: space-between; */ /* Let flex properties of children handle spacing */
    gap: 12px;
    z-index: 200;
    pointer-events: none;
    flex-wrap: wrap;
}

.map-toolbar > * {
    pointer-events: auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(226, 232, 240, 0.8);
}

/* 搜索框 */
.search-container {
    flex: 1;
    min-width: 280px; /* from zhuye.html */
    position: relative;
    /* background, border-radius, box-shadow, backdrop-filter, border handled by .map-toolbar > * */
}

.search-box { /* Renamed from .search-input */
    width: 100%;
    padding: 10px 16px 10px 40px;
    border: none; /* border is on container via .map-toolbar > * */
    /* border-radius: var(--radius-md); */ /* Handled by container */
    font-size: 14px;
    background: transparent; /* Important for frosted glass effect from container */
    transition: var(--transition-fast);
    height: 44px; /* from zhuye.html */
}

.search-box:focus {
    outline: none;
    background: rgba(255, 255, 255, 1); /* Solid background on focus */
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-icon {
    position: absolute;
    left: 14px; /* from zhuye.html */
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    pointer-events: none;
    font-size: 16px; /* from zhuye.html */
}

/* 工具栏按钮组 */
.toolbar-group {
    display: flex;
    gap: 8px; /* from zhuye.html */
    align-items: center; /* from zhuye.html */
    padding: 6px; /* from zhuye.html */
    /* background, border-radius, box-shadow, backdrop-filter, border handled by .map-toolbar > * */
}

.toolbar-btn {
    padding: 10px 16px; /* from zhuye.html */
    border: none;
    border-radius: var(--radius-sm); /* Individual radius for buttons inside group */
    cursor: pointer;
    font-size: 13px; /* from zhuye.html */
    font-weight: 500; /* from zhuye.html */
    transition: var(--transition-fast);
    white-space: nowrap; /* from zhuye.html */
    height: 36px; /* from zhuye.html */
    line-height: 1; /* from zhuye.html */
    background: transparent; /* Default, specific styles below */
    color: var(--text-secondary); /* Default color */
}

/* General button styles from zhuye.html, applied if classes match in index.html */
.btn-primary {
    background: var(--primary-color);
    color: white;
}
.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color); /* Overrides border:none from .toolbar-btn if also .btn-secondary */
}
.btn-secondary:hover {
    background: var(--text-secondary);
    color: white;
    transform: translateY(-1px);
}

.toolbar-btn.active { /* This was specific to toolbar-btn in modular, keep if still used */
    background: var(--primary-color);
    color: white;
}
.toolbar-btn:not(.btn-primary):not(.btn-secondary):hover {
     background: rgba(0,0,0,0.05); /* Replicate old hover for generic toolbar buttons */
     color: var(--primary-color);
}


/* 缩放控制 */
.zoom-controls {
    display: flex;
    align-items: center;
    gap: 4px; /* from zhuye.html */
    padding: 6px; /* from zhuye.html */
    /* background, border-radius, box-shadow, backdrop-filter, border handled by .map-toolbar > * */
}

.zoom-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--bg-primary); /* from zhuye.html */
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    font-size: 16px; /* from zhuye.html */
    font-weight: 600; /* from zhuye.html */
}

.zoom-btn:hover {
    background: var(--primary-color); /* from zhuye.html */
    color: white;
    transform: translateY(-1px); /* from zhuye.html */
}

.zoom-display { /* Renamed from .zoom-level */
    background: var(--bg-primary); /* from zhuye.html */
    padding: 8px 12px; /* from zhuye.html */
    border-radius: var(--radius-sm); /* from zhuye.html */
    font-size: 13px; /* from zhuye.html */
    font-weight: 600; /* from zhuye.html */
    color: var(--text-primary);
    min-width: 70px; /* from zhuye.html */
    text-align: center;
}

.reset-btn {
    padding: 8px 12px; /* from zhuye.html */
    border: none;
    background: var(--bg-primary); /* from zhuye.html */
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 12px; /* from zhuye.html */
    transition: var(--transition-fast);
    height: 32px; /* from zhuye.html */
}

.reset-btn:hover {
    background: var(--primary-color); /* from zhuye.html */
    color: white;
    transform: translateY(-1px); /* from zhuye.html */
}


/* 底部浮动工具栏 */
.bottom-floating-toolbar {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between; /* from zhuye.html */
    align-items: center; /* from zhuye.html */
    z-index: 200; /* from zhuye.html */
    pointer-events: none; /* from zhuye.html */
}

.floating-actions {
    display: flex;
    gap: 12px; /* from zhuye.html */
    pointer-events: auto; /* from zhuye.html */
    background: rgba(255, 255, 255, 0.95); /* Unified style from zhuye.html */
    padding: 8px; /* from zhuye.html */
    border-radius: var(--radius-md); /* Unified style from zhuye.html */
    box-shadow: var(--shadow-md); /* Unified style from zhuye.html */
    backdrop-filter: blur(8px); /* Unified style from zhuye.html */
    border: 1px solid rgba(226, 232, 240, 0.8); /* Unified style from zhuye.html */
}

.floating-btn {
    width: 44px; /* from zhuye.html */
    height: 44px; /* from zhuye.html */
    background: transparent;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    color: var(--text-secondary);
    font-size: 18px; /* from zhuye.html */
    position: relative;
}

.floating-btn:hover {
    background: var(--primary-color); /* from zhuye.html */
    color: white;
    transform: translateY(-2px); /* from zhuye.html */
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3); /* from zhuye.html */
}

.floating-btn:hover::after { /* Tooltip from zhuye.html */
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(15, 23, 42, 0.9);
    color: white;
    padding: 6px 10px;
    border-radius: var(--radius-sm);
    font-size: 12px;
    white-space: nowrap;
    margin-bottom: 8px;
    opacity: 1;
    pointer-events: none;
    z-index: 1000;
}

/* Removed .floating-btn-tooltip as ::after is used now */

.floating-version {
    pointer-events: auto; /* from zhuye.html */
    background: rgba(255, 255, 255, 0.95); /* Unified style from zhuye.html */
    padding: 12px 16px; /* from zhuye.html */
    border-radius: var(--radius-md); /* Unified style from zhuye.html */
    box-shadow: var(--shadow-md); /* Unified style from zhuye.html */
    backdrop-filter: blur(8px); /* Unified style from zhuye.html */
    border: 1px solid rgba(226, 232, 240, 0.8); /* Unified style from zhuye.html */
    display: flex; /* from zhuye.html */
    align-items: center; /* from zhuye.html */
    gap: 8px; /* from zhuye.html */
    font-size: 13px; /* from zhuye.html */
    color: var(--text-secondary);
}

.version-badge {
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 11px;
    font-weight: 600;
}