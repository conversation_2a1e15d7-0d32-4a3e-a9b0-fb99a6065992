/**
 * shop-manager.js - 店铺管理器
 * 处理店铺的绘制、编辑和管理
 */

import { showStatusMessage } from './ui-utils.js';

class ShopManager {
    constructor(mapController) {
        this.mapController = mapController;
        this.shops = [];
        this.isDrawing = false;
        this.currentShape = null;
        this.drawingMode = 'rectangle';
        this.currentPoints = [];
        this.selectedShop = null;
    }

    /**
     * 开始绘制店铺
     * @param {Object} startPosition - 起始位置
     */
    startDrawing(startPosition) {
        this.showDrawingModeDialog((mode) => {
            this.drawingMode = mode;
            this.isDrawing = true;
            this.currentPoints = [startPosition];

            this.addDrawingListeners();
            showStatusMessage(`开始绘制${this.getShapeName(mode)}店铺区域，点击完成绘制`);
        });
    }

    /**
     * 显示绘制模式选择对话框
     */
    showDrawingModeDialog(onSelect) {
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay shop-drawing-modal';
        overlay.style.cssText = `
            display: flex !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(0,0,0,0.5) !important;
            z-index: 1000 !important;
            justify-content: center !important;
            align-items: center !important;
        `;

        const modal = document.createElement('div');
        modal.style.cssText = `
            background: #fff !important;
            padding: 24px !important;
            border-radius: 8px !important;
            min-width: 400px !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3) !important;
        `;

        modal.innerHTML = `
            <div class="modal-header" style="margin-bottom: 20px;">
                <h2 style="margin: 0; color: #333;">选择店铺区域形状</h2>
                <p style="margin: 8px 0 0 0; color: #666; font-size: 14px;">
                    选择要绘制的店铺区域形状类型
                </p>
            </div>

            <div class="shape-options" style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                <div class="shape-option" data-shape="rectangle" style="
                    padding: 20px; border: 2px solid #e2e8f0; border-radius: 8px; cursor: pointer;
                    text-align: center; transition: all 0.2s ease;
                ">
                    <div style="font-size: 32px; margin-bottom: 8px;">🏪</div>
                    <div style="font-weight: 500;">矩形店铺</div>
                    <div style="font-size: 12px; color: #666;">标准店铺布局</div>
                </div>

                <div class="shape-option" data-shape="polygon" style="
                    padding: 20px; border: 2px solid #e2e8f0; border-radius: 8px; cursor: pointer;
                    text-align: center; transition: all 0.2s ease;
                ">
                    <div style="font-size: 32px; margin-bottom: 8px;">🏬</div>
                    <div style="font-weight: 500;">不规则店铺</div>
                    <div style="font-size: 12px; color: #666;">自定义形状</div>
                </div>
            </div>

            <div style="margin-top: 20px; text-align: right;">
                <button id="cancelShopDrawing" style="
                    background: #6b7280; color: white; border: none; padding: 10px 20px;
                    border-radius: 6px; cursor: pointer; font-size: 14px;
                ">取消</button>
            </div>
        `;

        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // 绑定事件
        modal.querySelectorAll('.shape-option').forEach(option => {
            option.addEventListener('mouseenter', () => {
                option.style.borderColor = '#10b981';
                option.style.backgroundColor = '#f0fdf4';
            });

            option.addEventListener('mouseleave', () => {
                option.style.borderColor = '#e2e8f0';
                option.style.backgroundColor = 'transparent';
            });

            option.addEventListener('click', () => {
                const shape = option.dataset.shape;
                overlay.remove();
                onSelect(shape);
            });
        });

        modal.querySelector('#cancelShopDrawing').addEventListener('click', () => {
            overlay.remove();
        });

        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
            }
        });
    }

    /**
     * 获取形状名称
     */
    getShapeName(shape) {
        const names = {
            rectangle: '矩形',
            polygon: '多边形'
        };
        return names[shape] || '';
    }

    /**
     * 添加绘制事件监听
     */
    addDrawingListeners() {
        const mapContainer = document.getElementById('mapContainer');
        if (!mapContainer) return;

        this.drawingClickHandler = (e) => this.handleDrawingClick(e);
        this.drawingKeyHandler = (e) => this.handleDrawingKey(e);

        mapContainer.addEventListener('click', this.drawingClickHandler);
        document.addEventListener('keydown', this.drawingKeyHandler);
    }

    /**
     * 移除绘制事件监听
     */
    removeDrawingListeners() {
        const mapContainer = document.getElementById('mapContainer');
        if (!mapContainer) return;

        mapContainer.removeEventListener('click', this.drawingClickHandler);
        document.removeEventListener('keydown', this.drawingKeyHandler);
    }

    /**
     * 处理绘制点击事件
     */
    handleDrawingClick(e) {
        if (!this.isDrawing) return;

        e.stopPropagation();

        const rect = e.currentTarget.getBoundingClientRect();
        const position = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };

        if (this.drawingMode === 'rectangle') {
            this.handleRectangleDrawing(position);
        } else if (this.drawingMode === 'polygon') {
            this.handlePolygonDrawing(position);
        }
    }

    /**
     * 处理矩形绘制
     */
    handleRectangleDrawing(position) {
        if (this.currentPoints.length === 1) {
            this.currentPoints.push(position);
            this.finishDrawing();
        }
    }

    /**
     * 处理多边形绘制
     */
    handlePolygonDrawing(position) {
        this.currentPoints.push(position);
        // 多边形需要至少3个点
        if (this.currentPoints.length >= 3) {
            showStatusMessage('按Enter完成绘制，或继续点击添加更多点');
        }
    }

    /**
     * 处理绘制键盘事件
     */
    handleDrawingKey(e) {
        if (!this.isDrawing) return;

        if (e.key === 'Escape') {
            this.cancelDrawing();
        } else if (e.key === 'Enter' && this.drawingMode === 'polygon' && this.currentPoints.length >= 3) {
            this.finishDrawing();
        }
    }

    /**
     * 完成绘制
     */
    finishDrawing() {
        if (this.currentPoints.length < 2) return;

        const shopData = {
            id: `shop-${Date.now()}`,
            name: '新店铺',
            shape: this.drawingMode,
            points: this.currentPoints,
            area: this.calculateArea(),
            category: 'retail',
            owner: '',
            phone: '',
            businessHours: '09:00-21:00',
            rent: 0,
            status: 'operating',
            description: '',
            createTime: new Date().toISOString()
        };

        this.showShopConfigDialog(shopData, (configuredShop) => {
            this.shops.push(configuredShop);
            this.renderShop(configuredShop);
            this.saveShop(configuredShop);
            showStatusMessage(`已添加店铺: ${configuredShop.name}`);
        });

        this.resetDrawing();
    }

    /**
     * 取消绘制
     */
    cancelDrawing() {
        this.resetDrawing();
        showStatusMessage('已取消绘制');
    }

    /**
     * 重置绘制状态
     */
    resetDrawing() {
        this.isDrawing = false;
        this.currentPoints = [];
        this.removeDrawingListeners();
    }

    /**
     * 计算区域面积
     */
    calculateArea() {
        if (this.drawingMode === 'rectangle' && this.currentPoints.length === 2) {
            const width = Math.abs(this.currentPoints[1].x - this.currentPoints[0].x);
            const height = Math.abs(this.currentPoints[1].y - this.currentPoints[0].y);
            return width * height;
        } else if (this.drawingMode === 'polygon') {
            let area = 0;
            const points = this.currentPoints;
            for (let i = 0; i < points.length; i++) {
                const j = (i + 1) % points.length;
                area += points[i].x * points[j].y;
                area -= points[j].x * points[i].y;
            }
            return Math.abs(area) / 2;
        }
        return 0;
    }

    /**
     * 显示店铺配置对话框
     */
    showShopConfigDialog(shopData, onConfirm) {
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay shop-config-modal';
        overlay.style.cssText = `
            display: flex !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(0,0,0,0.5) !important;
            z-index: 1000 !important;
            justify-content: center !important;
            align-items: center !important;
        `;

        const modal = document.createElement('div');
        modal.style.cssText = `
            background: #fff !important;
            padding: 24px !important;
            border-radius: 8px !important;
            min-width: 500px !important;
            max-width: 600px !important;
            max-height: 80vh !important;
            overflow-y: auto !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3) !important;
        `;

        modal.innerHTML = `
            <div class="modal-header" style="margin-bottom: 20px;">
                <h2 style="margin: 0; color: #333;">店铺信息配置</h2>
                <p style="margin: 8px 0 0 0; color: #666; font-size: 14px;">
                    配置店铺的基本信息和经营状态
                </p>
            </div>

            <div class="modal-body">
                <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                    <div class="form-group" style="flex: 2;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">店铺名称</label>
                        <input type="text" id="shopName" value="${shopData.name}" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                    </div>
                    <div class="form-group" style="flex: 1;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">店铺类型</label>
                        <select id="shopCategory" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                            <option value="retail" ${shopData.category === 'retail' ? 'selected' : ''}>🛍️ 零售</option>
                            <option value="restaurant" ${shopData.category === 'restaurant' ? 'selected' : ''}>🍽️ 餐饮</option>
                            <option value="service" ${shopData.category === 'service' ? 'selected' : ''}>🔧 服务</option>
                            <option value="entertainment" ${shopData.category === 'entertainment' ? 'selected' : ''}>🎮 娱乐</option>
                            <option value="education" ${shopData.category === 'education' ? 'selected' : ''}>📚 教育</option>
                            <option value="other" ${shopData.category === 'other' ? 'selected' : ''}>📦 其他</option>
                        </select>
                    </div>
                </div>

                <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                    <div class="form-group" style="flex: 1;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">店主姓名</label>
                        <input type="text" id="shopOwner" value="${shopData.owner}" placeholder="输入店主姓名" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                    </div>
                    <div class="form-group" style="flex: 1;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">联系电话</label>
                        <input type="tel" id="shopPhone" value="${shopData.phone}" placeholder="输入联系电话" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                    </div>
                </div>

                <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                    <div class="form-group" style="flex: 1;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">营业时间</label>
                        <input type="text" id="shopHours" value="${shopData.businessHours}" placeholder="09:00-21:00" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                    </div>
                    <div class="form-group" style="flex: 1;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">经营状态</label>
                        <select id="shopStatus" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                            <option value="operating" ${shopData.status === 'operating' ? 'selected' : ''}>🟢 正常营业</option>
                            <option value="renovation" ${shopData.status === 'renovation' ? 'selected' : ''}>🟡 装修中</option>
                            <option value="closed" ${shopData.status === 'closed' ? 'selected' : ''}>🔴 暂停营业</option>
                            <option value="vacant" ${shopData.status === 'vacant' ? 'selected' : ''}>⚫ 空置</option>
                        </select>
                    </div>
                </div>

                <div class="form-group" style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 4px; font-weight: 500;">月租金 (元)</label>
                    <input type="number" id="shopRent" value="${shopData.rent}" min="0" step="100" style="
                        width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                        font-size: 14px; box-sizing: border-box;
                    ">
                </div>

                <div class="form-group" style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 4px; font-weight: 500;">店铺描述</label>
                    <textarea id="shopDescription" rows="3" placeholder="输入店铺描述..." style="
                        width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                        font-size: 14px; resize: vertical; box-sizing: border-box;
                    ">${shopData.description}</textarea>
                </div>

                <div class="info-section" style="background: #f8fafc; padding: 12px; border-radius: 6px;">
                    <h4 style="margin: 0 0 8px 0; font-size: 14px; color: #374151;">区域信息</h4>
                    <div style="font-size: 12px; color: #6b7280;">
                        <div>形状: ${this.getShapeName(shopData.shape)}</div>
                        <div>面积: ${Math.round(shopData.area)} 平方像素</div>
                        <div>创建时间: ${new Date(shopData.createTime).toLocaleString()}</div>
                    </div>
                </div>
            </div>

            <div class="modal-actions" style="margin-top: 24px; text-align: right; display: flex; gap: 12px; justify-content: flex-end;">
                <button id="cancelShopBtn" style="
                    background: #6b7280; color: white; border: none; padding: 10px 20px;
                    border-radius: 6px; cursor: pointer; font-size: 14px;
                ">取消</button>
                <button id="confirmShopBtn" style="
                    background: #10b981; color: white; border: none; padding: 10px 20px;
                    border-radius: 6px; cursor: pointer; font-size: 14px;
                ">确认添加</button>
            </div>
        `;

        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        this.bindShopConfigEvents(overlay, shopData, onConfirm);
    }

    /**
     * 绑定店铺配置事件
     */
    bindShopConfigEvents(overlay, shopData, onConfirm) {
        const confirmBtn = overlay.querySelector('#confirmShopBtn');
        const cancelBtn = overlay.querySelector('#cancelShopBtn');

        confirmBtn.addEventListener('click', () => {
            const configuredShop = {
                ...shopData,
                name: overlay.querySelector('#shopName').value.trim(),
                category: overlay.querySelector('#shopCategory').value,
                owner: overlay.querySelector('#shopOwner').value.trim(),
                phone: overlay.querySelector('#shopPhone').value.trim(),
                businessHours: overlay.querySelector('#shopHours').value.trim(),
                status: overlay.querySelector('#shopStatus').value,
                rent: parseFloat(overlay.querySelector('#shopRent').value) || 0,
                description: overlay.querySelector('#shopDescription').value.trim()
            };

            if (!configuredShop.name) {
                alert('请输入店铺名称');
                return;
            }

            overlay.remove();
            onConfirm(configuredShop);
        });

        cancelBtn.addEventListener('click', () => {
            overlay.remove();
        });

        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
            }
        });
    }

    /**
     * 渲染店铺
     */
    renderShop(shop) {
        const mapViewport = document.getElementById('mapViewport');
        if (!mapViewport) return;

        const shopElement = document.createElement('div');
        shopElement.className = 'shop-area';
        shopElement.id = `shop-${shop.id}`;

        if (shop.shape === 'rectangle') {
            this.renderRectangleShop(shopElement, shop);
        } else if (shop.shape === 'polygon') {
            this.renderPolygonShop(shopElement, shop);
        }

        mapViewport.appendChild(shopElement);
    }

    /**
     * 渲染矩形店铺
     */
    renderRectangleShop(element, shop) {
        const [p1, p2] = shop.points;
        const left = Math.min(p1.x, p2.x);
        const top = Math.min(p1.y, p2.y);
        const width = Math.abs(p2.x - p1.x);
        const height = Math.abs(p2.y - p1.y);

        element.style.cssText = `
            position: absolute;
            left: ${left}px;
            top: ${top}px;
            width: ${width}px;
            height: ${height}px;
            border: 2px solid ${this.getShopColor(shop.status)};
            background: ${this.getShopBackground(shop.status)};
            pointer-events: auto;
            cursor: pointer;
            z-index: 50;
        `;

        this.addShopLabel(element, shop);
    }

    /**
     * 渲染多边形店铺
     */
    renderPolygonShop(element, shop) {
        // 计算边界框
        const xs = shop.points.map(p => p.x);
        const ys = shop.points.map(p => p.y);
        const minX = Math.min(...xs);
        const minY = Math.min(...ys);
        const maxX = Math.max(...xs);
        const maxY = Math.max(...ys);

        element.style.cssText = `
            position: absolute;
            left: ${minX}px;
            top: ${minY}px;
            width: ${maxX - minX}px;
            height: ${maxY - minY}px;
            pointer-events: auto;
            cursor: pointer;
            z-index: 50;
        `;

        // 创建SVG多边形
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.style.cssText = 'width: 100%; height: 100%;';

        const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
        const points = shop.points.map(p => `${p.x - minX},${p.y - minY}`).join(' ');
        polygon.setAttribute('points', points);
        polygon.setAttribute('fill', this.getShopBackground(shop.status));
        polygon.setAttribute('stroke', this.getShopColor(shop.status));
        polygon.setAttribute('stroke-width', '2');

        svg.appendChild(polygon);
        element.appendChild(svg);

        this.addShopLabel(element, shop);
    }

    /**
     * 获取店铺颜色
     */
    getShopColor(status) {
        const colors = {
            operating: '#10b981',
            renovation: '#f59e0b',
            closed: '#ef4444',
            vacant: '#6b7280'
        };
        return colors[status] || '#10b981';
    }

    /**
     * 获取店铺背景色
     */
    getShopBackground(status) {
        const backgrounds = {
            operating: 'rgba(16, 185, 129, 0.1)',
            renovation: 'rgba(245, 158, 11, 0.1)',
            closed: 'rgba(239, 68, 68, 0.1)',
            vacant: 'rgba(107, 114, 128, 0.1)'
        };
        return backgrounds[status] || 'rgba(16, 185, 129, 0.1)';
    }

    /**
     * 添加店铺标签
     */
    addShopLabel(element, shop) {
        const label = document.createElement('div');
        label.style.cssText = `
            position: absolute;
            top: 4px;
            left: 4px;
            background: ${this.getShopColor(shop.status)};
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            pointer-events: none;
            max-width: calc(100% - 16px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        `;

        const categoryIcon = this.getCategoryIcon(shop.category);
        label.innerHTML = `${categoryIcon} ${shop.name}`;
        element.appendChild(label);

        // 添加点击事件
        element.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectShop(shop);
        });

        // 添加悬停提示
        element.addEventListener('mouseenter', () => {
            this.showShopTooltip(shop, element);
        });

        element.addEventListener('mouseleave', () => {
            this.hideShopTooltip();
        });
    }

    /**
     * 获取类别图标
     */
    getCategoryIcon(category) {
        const icons = {
            retail: '🛍️',
            restaurant: '🍽️',
            service: '🔧',
            entertainment: '🎮',
            education: '📚',
            other: '📦'
        };
        return icons[category] || '🏪';
    }

    /**
     * 选择店铺
     */
    selectShop(shop) {
        this.selectedShop = shop;
        showStatusMessage(`已选择店铺: ${shop.name}`);
        // TODO: 实现店铺选择高亮效果
    }

    /**
     * 显示店铺提示框
     */
    showShopTooltip(shop, element) {
        // TODO: 实现店铺提示框
        console.log('显示店铺提示框:', shop);
    }

    /**
     * 隐藏店铺提示框
     */
    hideShopTooltip() {
        // TODO: 实现隐藏提示框
    }

    /**
     * 保存店铺到存储
     */
    async saveShop(shop) {
        try {
            if (this.mapController.storage) {
                // 这里可以扩展存储管理器来支持店铺数据
                console.log('保存店铺:', shop);
            }
        } catch (error) {
            console.error('保存店铺失败:', error);
            showStatusMessage('保存店铺失败', 3000);
        }
    }
}

export default ShopManager;
