/**
 * shop-manager.js - 店铺管理器
 * 处理店铺的绘制、编辑和管理
 */

import { showStatusMessage } from './ui-utils.js';

class ShopManager {
    constructor(mapController) {
        this.mapController = mapController;
        this.shops = [];
        this.isDrawing = false;
        this.currentShape = null;
        this.drawingMode = 'rectangle';
        this.currentPoints = [];
        this.selectedShop = null;
    }

    /**
     * 计算地图坐标（考虑缩放和平移）
     * @param {Object} screenPosition - 屏幕位置
     * @returns {Object} 地图坐标
     */
    calculateMapCoordinates(screenPosition) {
        const zoom = this.mapController.zoom || 1;
        const panX = this.mapController.panX || 0;
        const panY = this.mapController.panY || 0;

        return {
            x: (screenPosition.x - panX) / zoom,
            y: (screenPosition.y - panY) / zoom
        };
    }

    /**
     * 将地图坐标转换为屏幕坐标（用于预览显示）
     * @param {Object} mapPosition - 地图坐标
     * @returns {Object} 屏幕坐标
     */
    mapToScreenCoordinates(mapPosition) {
        const zoom = this.mapController.zoom || 1;
        const panX = this.mapController.panX || 0;
        const panY = this.mapController.panY || 0;

        return {
            x: mapPosition.x * zoom + panX,
            y: mapPosition.y * zoom + panY
        };
    }

    /**
     * 开始绘制店铺
     * @param {Object} startPosition - 起始位置
     */
    startDrawing(startPosition) {
        console.log('开始绘制店铺');

        // 清理任何现有的对话框
        const existingModals = document.querySelectorAll('.modal-overlay');
        existingModals.forEach(modal => modal.remove());

        // 直接开始多边形绘制模式
        this.drawingMode = 'polygon';
        this.isDrawing = true;
        this.currentPoints = [];

        // 设置绘制样式
        this.setDrawingCursor();

        // 添加绘制事件监听
        this.addDrawingListeners();

        // 创建坐标虚线
        this.createCrosshair();

        console.log('店铺绘制状态设置完成，isDrawing:', this.isDrawing);

        showStatusMessage('🏪 店铺绘制模式：点击设置点位（最少3个点），右键完成绘制');
    }

    /**
     * 设置绘制光标
     */
    setDrawingCursor() {
        const mapContainer = document.getElementById('mapContainer');
        if (mapContainer) {
            mapContainer.style.cursor = 'crosshair';
            mapContainer.classList.add('drawing-mode');
        }
    }

    /**
     * 恢复默认光标
     */
    resetCursor() {
        const mapContainer = document.getElementById('mapContainer');
        if (mapContainer) {
            mapContainer.style.cursor = '';
            mapContainer.classList.remove('drawing-mode');
        }
    }

    /**
     * 创建坐标虚线
     */
    createCrosshair() {
        const mapContainer = document.getElementById('mapContainer');
        if (!mapContainer) return;

        // 创建十字线容器
        this.crosshairContainer = document.createElement('div');
        this.crosshairContainer.className = 'crosshair-container';
        this.crosshairContainer.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        `;

        // 创建水平线
        this.horizontalLine = document.createElement('div');
        this.horizontalLine.className = 'crosshair-horizontal';
        this.horizontalLine.style.cssText = `
            position: absolute;
            width: 100%;
            height: 1px;
            background: #10b981;
            border-top: 1px dashed #10b981;
            opacity: 0.7;
            display: none;
        `;

        // 创建垂直线
        this.verticalLine = document.createElement('div');
        this.verticalLine.className = 'crosshair-vertical';
        this.verticalLine.style.cssText = `
            position: absolute;
            width: 1px;
            height: 100%;
            background: #10b981;
            border-left: 1px dashed #10b981;
            opacity: 0.7;
            display: none;
        `;

        // 创建坐标显示
        this.coordinateDisplay = document.createElement('div');
        this.coordinateDisplay.className = 'coordinate-display';
        this.coordinateDisplay.style.cssText = `
            position: absolute;
            background: rgba(16, 185, 129, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-family: monospace;
            pointer-events: none;
            z-index: 1001;
            display: none;
        `;

        this.crosshairContainer.appendChild(this.horizontalLine);
        this.crosshairContainer.appendChild(this.verticalLine);
        this.crosshairContainer.appendChild(this.coordinateDisplay);
        mapContainer.appendChild(this.crosshairContainer);
    }

    /**
     * 更新坐标虚线位置
     * @param {number} screenX - 屏幕X坐标
     * @param {number} screenY - 屏幕Y坐标
     * @param {Object} mapCoords - 地图坐标（可选）
     */
    updateCrosshair(screenX, screenY, mapCoords = null) {
        if (!this.crosshairContainer) return;

        this.horizontalLine.style.top = `${screenY}px`;
        this.horizontalLine.style.display = 'block';

        this.verticalLine.style.left = `${screenX}px`;
        this.verticalLine.style.display = 'block';

        this.coordinateDisplay.style.left = `${screenX + 10}px`;
        this.coordinateDisplay.style.top = `${screenY - 25}px`;
        this.coordinateDisplay.style.display = 'block';

        // 显示地图坐标而不是屏幕坐标
        if (mapCoords) {
            this.coordinateDisplay.textContent = `(${Math.round(mapCoords.x)}, ${Math.round(mapCoords.y)})`;
        } else {
            this.coordinateDisplay.textContent = `(${Math.round(screenX)}, ${Math.round(screenY)})`;
        }
    }

    /**
     * 清除坐标虚线
     */
    clearCrosshair() {
        if (this.crosshairContainer && this.crosshairContainer.parentNode) {
            this.crosshairContainer.parentNode.removeChild(this.crosshairContainer);
        }
        this.crosshairContainer = null;
        this.horizontalLine = null;
        this.verticalLine = null;
        this.coordinateDisplay = null;
    }

    /**
     * 显示绘制模式选择对话框
     */
    showDrawingModeDialog(onSelect) {
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay shop-drawing-modal';
        overlay.style.cssText = `
            display: flex !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(0,0,0,0.5) !important;
            z-index: 1000 !important;
            justify-content: center !important;
            align-items: center !important;
        `;

        const modal = document.createElement('div');
        modal.style.cssText = `
            background: #fff !important;
            padding: 24px !important;
            border-radius: 8px !important;
            min-width: 400px !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3) !important;
        `;

        modal.innerHTML = `
            <div class="modal-header" style="margin-bottom: 20px;">
                <h2 style="margin: 0; color: #333;">选择店铺区域形状</h2>
                <p style="margin: 8px 0 0 0; color: #666; font-size: 14px;">
                    选择要绘制的店铺区域形状类型
                </p>
            </div>

            <div class="shape-options" style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                <div class="shape-option" data-shape="rectangle" style="
                    padding: 20px; border: 2px solid #e2e8f0; border-radius: 8px; cursor: pointer;
                    text-align: center; transition: all 0.2s ease;
                ">
                    <div style="font-size: 32px; margin-bottom: 8px;">🏪</div>
                    <div style="font-weight: 500;">矩形店铺</div>
                    <div style="font-size: 12px; color: #666;">标准店铺布局</div>
                </div>

                <div class="shape-option" data-shape="polygon" style="
                    padding: 20px; border: 2px solid #e2e8f0; border-radius: 8px; cursor: pointer;
                    text-align: center; transition: all 0.2s ease;
                ">
                    <div style="font-size: 32px; margin-bottom: 8px;">🏬</div>
                    <div style="font-weight: 500;">不规则店铺</div>
                    <div style="font-size: 12px; color: #666;">自定义形状</div>
                </div>
            </div>

            <div style="margin-top: 20px; text-align: right;">
                <button id="cancelShopDrawing" style="
                    background: #6b7280; color: white; border: none; padding: 10px 20px;
                    border-radius: 6px; cursor: pointer; font-size: 14px;
                ">取消</button>
            </div>
        `;

        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // 绑定事件
        modal.querySelectorAll('.shape-option').forEach(option => {
            option.addEventListener('mouseenter', () => {
                option.style.borderColor = '#10b981';
                option.style.backgroundColor = '#f0fdf4';
            });

            option.addEventListener('mouseleave', () => {
                option.style.borderColor = '#e2e8f0';
                option.style.backgroundColor = 'transparent';
            });

            option.addEventListener('click', () => {
                const shape = option.dataset.shape;
                overlay.remove();
                onSelect(shape);
            });
        });

        modal.querySelector('#cancelShopDrawing').addEventListener('click', () => {
            overlay.remove();
        });

        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
            }
        });
    }

    /**
     * 获取形状名称
     */
    getShapeName(shape) {
        const names = {
            rectangle: '矩形',
            polygon: '多边形'
        };
        return names[shape] || '';
    }

    /**
     * 添加绘制事件监听
     */
    addDrawingListeners() {
        const mapContainer = document.getElementById('mapContainer');
        if (!mapContainer) {
            console.error('找不到mapContainer元素');
            return;
        }

        console.log('添加店铺绘制事件监听器');

        this.drawingClickHandler = (e) => this.handleDrawingClick(e);
        this.drawingRightClickHandler = (e) => this.handleDrawingRightClick(e);
        this.drawingMouseMoveHandler = (e) => this.handleDrawingMouseMove(e);
        this.drawingKeyHandler = (e) => this.handleDrawingKey(e);

        mapContainer.addEventListener('click', this.drawingClickHandler, true);
        mapContainer.addEventListener('contextmenu', this.drawingRightClickHandler, true);
        mapContainer.addEventListener('mousemove', this.drawingMouseMoveHandler);
        document.addEventListener('keydown', this.drawingKeyHandler);

        console.log('店铺绘制事件监听器添加完成');
    }

    /**
     * 移除绘制事件监听
     */
    removeDrawingListeners() {
        const mapContainer = document.getElementById('mapContainer');
        if (!mapContainer) return;

        console.log('移除店铺绘制事件监听器');

        mapContainer.removeEventListener('click', this.drawingClickHandler, true);
        mapContainer.removeEventListener('contextmenu', this.drawingRightClickHandler, true);
        mapContainer.removeEventListener('mousemove', this.drawingMouseMoveHandler);
        document.removeEventListener('keydown', this.drawingKeyHandler);
    }

    /**
     * 处理绘制点击事件（左键添加点位）
     */
    handleDrawingClick(e) {
        if (!this.isDrawing || e.button !== 0) return; // 只处理左键

        e.preventDefault();
        e.stopPropagation();

        const rect = e.currentTarget.getBoundingClientRect();
        const screenPosition = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };

        // 转换为地图坐标（考虑缩放和平移）
        const position = this.calculateMapCoordinates(screenPosition);

        console.log('添加店铺点位:', position, '屏幕坐标:', screenPosition, '当前点位数量:', this.currentPoints.length);

        // 添加点位
        this.currentPoints.push(position);
        this.updatePreview();

        // 更新状态提示
        if (this.currentPoints.length < 3) {
            showStatusMessage(`🏪 已添加${this.currentPoints.length}个点位，还需${3 - this.currentPoints.length}个点位（右键完成绘制）`);
        } else {
            showStatusMessage(`🏪 已添加${this.currentPoints.length}个点位，右键完成绘制`);
        }
    }

    /**
     * 处理绘制右键事件（完成绘制）
     */
    handleDrawingRightClick(e) {
        if (!this.isDrawing) return;

        e.preventDefault();
        e.stopPropagation();

        console.log('右键完成店铺绘制，当前点位数量:', this.currentPoints.length);

        // 检查是否有足够的点位
        if (this.currentPoints.length < 3) {
            showStatusMessage('⚠️ 至少需要3个点位才能完成绘制');
            return;
        }

        // 完成绘制
        this.finishDrawing();
    }

    /**
     * 处理绘制鼠标移动
     */
    handleDrawingMouseMove(e) {
        if (!this.isDrawing) return;

        const rect = e.currentTarget.getBoundingClientRect();
        const screenPos = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };

        // 转换为地图坐标（考虑缩放和平移）
        const currentPos = this.calculateMapCoordinates(screenPos);

        // 更新坐标虚线（使用屏幕坐标显示，但传递地图坐标用于显示）
        this.updateCrosshair(screenPos.x, screenPos.y, currentPos);

        // 更新预览（使用地图坐标）
        this.updatePreview(currentPos);
    }

    /**
     * 处理矩形绘制
     */
    handleRectangleDrawing(position) {
        if (this.currentPoints.length === 1) {
            this.currentPoints.push(position);
            this.finishDrawing();
        }
    }

    /**
     * 处理多边形绘制
     */
    handlePolygonDrawing(position) {
        // 检查是否是双击（点击位置很接近上一个点）
        if (this.currentPoints.length > 0) {
            const lastPoint = this.currentPoints[this.currentPoints.length - 1];
            const distance = Math.sqrt(
                Math.pow(position.x - lastPoint.x, 2) + Math.pow(position.y - lastPoint.y, 2)
            );

            // 如果距离小于10像素，认为是双击，完成绘制
            if (distance < 10 && this.currentPoints.length >= 3) {
                console.log('检测到双击，完成店铺多边形绘制');
                this.finishDrawing();
                return;
            }
        }

        this.currentPoints.push(position);

        console.log('店铺多边形点位数量:', this.currentPoints.length);

        // 多边形需要至少3个点
        if (this.currentPoints.length >= 3) {
            showStatusMessage(`已添加${this.currentPoints.length}个点，双击或按Enter完成绘制`);
        } else {
            showStatusMessage('继续点击添加更多点');
        }
    }

    /**
     * 处理绘制键盘事件
     */
    handleDrawingKey(e) {
        if (!this.isDrawing) return;

        if (e.key === 'Escape') {
            this.cancelDrawing();
        } else if (e.key === 'Enter' && this.drawingMode === 'polygon' && this.currentPoints.length >= 3) {
            this.finishDrawing();
        }
    }

    /**
     * 更新预览
     */
    updatePreview(currentPos = null) {
        this.clearPreview();

        const mapContainer = document.getElementById('mapContainer');
        if (!mapContainer || this.currentPoints.length === 0) return;

        // 创建预览元素
        this.previewElement = document.createElement('div');
        this.previewElement.className = 'shop-preview';
        this.previewElement.style.cssText = `
            position: absolute;
            pointer-events: none;
            z-index: 999;
            border: 2px dashed #10b981;
            background: rgba(16, 185, 129, 0.1);
        `;

        if (this.currentPoints.length >= 1) {
            // 多边形预览 - 转换地图坐标为屏幕坐标
            const mapPoints = [...this.currentPoints];
            if (currentPos) {
                mapPoints.push(currentPos);
            }

            // 转换为屏幕坐标
            const screenPoints = mapPoints.map(p => this.mapToScreenCoordinates(p));

            // 计算边界框
            const xs = screenPoints.map(p => p.x);
            const ys = screenPoints.map(p => p.y);
            const minX = Math.min(...xs);
            const minY = Math.min(...ys);
            const maxX = Math.max(...xs);
            const maxY = Math.max(...ys);

            this.previewElement.style.left = `${minX}px`;
            this.previewElement.style.top = `${minY}px`;
            this.previewElement.style.width = `${maxX - minX}px`;
            this.previewElement.style.height = `${maxY - minY}px`;
            this.previewElement.style.border = 'none';
            this.previewElement.style.background = 'none';

            // 创建SVG
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.style.cssText = 'width: 100%; height: 100%;';

            if (screenPoints.length >= 3) {
                // 多边形预览（3个或更多点）
                const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
                const pointsStr = screenPoints.map(p => `${p.x - minX},${p.y - minY}`).join(' ');
                polygon.setAttribute('points', pointsStr);
                polygon.setAttribute('fill', 'rgba(16, 185, 129, 0.1)');
                polygon.setAttribute('stroke', '#10b981');
                polygon.setAttribute('stroke-width', '2');
                polygon.setAttribute('stroke-dasharray', '5,5');
                svg.appendChild(polygon);
            } else if (screenPoints.length === 2) {
                // 线段预览（2个点）
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                line.setAttribute('x1', screenPoints[0].x - minX);
                line.setAttribute('y1', screenPoints[0].y - minY);
                line.setAttribute('x2', screenPoints[1].x - minX);
                line.setAttribute('y2', screenPoints[1].y - minY);
                line.setAttribute('stroke', '#10b981');
                line.setAttribute('stroke-width', '2');
                line.setAttribute('stroke-dasharray', '5,5');
                svg.appendChild(line);
            }

            this.previewElement.appendChild(svg);

            // 添加点位标记
            screenPoints.forEach((point, index) => {
                const pointMarker = document.createElement('div');
                pointMarker.style.cssText = `
                    position: absolute;
                    left: ${point.x - minX - 4}px;
                    top: ${point.y - minY - 4}px;
                    width: 8px;
                    height: 8px;
                    background: #10b981;
                    border: 2px solid white;
                    border-radius: 50%;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                `;
                this.previewElement.appendChild(pointMarker);
            });
        }

        mapContainer.appendChild(this.previewElement);
    }

    /**
     * 清除预览
     */
    clearPreview() {
        if (this.previewElement && this.previewElement.parentNode) {
            this.previewElement.parentNode.removeChild(this.previewElement);
        }
        this.previewElement = null;
    }

    /**
     * 完成绘制
     */
    finishDrawing() {
        if (this.currentPoints.length < 3) {
            showStatusMessage('至少需要3个点才能完成绘制');
            return;
        }

        console.log('完成店铺绘制，点位数量:', this.currentPoints.length);

        // 计算面积
        const area = this.calculatePolygonArea(this.currentPoints);

        const shopData = {
            id: `shop-${Date.now()}`,
            name: '新店铺',
            shape: 'polygon',
            points: [...this.currentPoints], // 复制数组，避免引用问题
            area: area,
            category: 'retail',
            owner: '',
            phone: '',
            businessHours: '09:00-21:00',
            rent: 0,
            status: 'operating',
            description: '',
            createTime: new Date().toISOString()
        };

        console.log('店铺数据:', shopData);

        // 先清理绘制状态
        this.resetDrawing();

        // 显示配置对话框
        this.showShopConfigDialog(shopData, (configuredShop) => {
            console.log('配置完成的店铺:', configuredShop);
            this.shops.push(configuredShop);
            this.renderShop(configuredShop);
            this.saveShop(configuredShop);
            showStatusMessage(`店铺 ${configuredShop.name} 已创建`);
        });
    }

    /**
     * 取消绘制
     */
    cancelDrawing() {
        this.resetDrawing();
        showStatusMessage('已取消绘制');
    }

    /**
     * 重置绘制状态
     */
    resetDrawing() {
        this.isDrawing = false;
        this.currentPoints = [];
        this.removeDrawingListeners();
        this.clearPreview();
        this.clearCrosshair();
        this.resetCursor();

        console.log('店铺绘制状态已重置');
    }

    /**
     * 计算多边形面积
     * @param {Array} points - 点位数组
     * @returns {number} 面积
     */
    calculatePolygonArea(points) {
        if (points.length < 3) return 0;

        // 使用鞋带公式计算多边形面积
        let area = 0;
        for (let i = 0; i < points.length; i++) {
            const j = (i + 1) % points.length;
            area += points[i].x * points[j].y;
            area -= points[j].x * points[i].y;
        }
        return Math.abs(area) / 2;
    }

    /**
     * 计算区域面积
     */
    calculateArea() {
        if (this.drawingMode === 'rectangle' && this.currentPoints.length === 2) {
            const width = Math.abs(this.currentPoints[1].x - this.currentPoints[0].x);
            const height = Math.abs(this.currentPoints[1].y - this.currentPoints[0].y);
            return width * height;
        } else if (this.drawingMode === 'polygon') {
            let area = 0;
            const points = this.currentPoints;
            for (let i = 0; i < points.length; i++) {
                const j = (i + 1) % points.length;
                area += points[i].x * points[j].y;
                area -= points[j].x * points[i].y;
            }
            return Math.abs(area) / 2;
        }
        return 0;
    }

    /**
     * 显示店铺配置对话框
     */
    showShopConfigDialog(shopData, onConfirm) {
        console.log('显示店铺配置对话框', shopData);

        try {
            const overlay = document.createElement('div');
        overlay.className = 'modal-overlay shop-config-modal';
        overlay.style.cssText = `
            display: flex !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background-color: rgba(0,0,0,0.7) !important;
            z-index: 9999999 !important;
            justify-content: center !important;
            align-items: center !important;
            visibility: visible !important;
            opacity: 1 !important;
        `;

        const modal = document.createElement('div');
        modal.style.cssText = `
            background: #fff !important;
            padding: 24px !important;
            border-radius: 8px !important;
            min-width: 500px !important;
            max-width: 600px !important;
            max-height: 80vh !important;
            overflow-y: auto !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3) !important;
        `;

        modal.innerHTML = `
            <div class="modal-header" style="margin-bottom: 20px;">
                <h2 style="margin: 0; color: #333;">🏪 店铺信息配置</h2>
                <p style="margin: 8px 0 0 0; color: #666; font-size: 14px;">
                    配置店铺的基本信息和经营状态
                </p>
            </div>

            <div class="modal-body">
                <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                    <div class="form-group" style="flex: 2;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">店铺名称</label>
                        <input type="text" id="shopName" value="${shopData.name}" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                    </div>
                    <div class="form-group" style="flex: 1;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">店铺类型</label>
                        <select id="shopCategory" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                            <option value="retail" ${shopData.category === 'retail' ? 'selected' : ''}>🛍️ 零售</option>
                            <option value="restaurant" ${shopData.category === 'restaurant' ? 'selected' : ''}>🍽️ 餐饮</option>
                            <option value="service" ${shopData.category === 'service' ? 'selected' : ''}>🔧 服务</option>
                            <option value="entertainment" ${shopData.category === 'entertainment' ? 'selected' : ''}>🎮 娱乐</option>
                            <option value="education" ${shopData.category === 'education' ? 'selected' : ''}>📚 教育</option>
                            <option value="other" ${shopData.category === 'other' ? 'selected' : ''}>📦 其他</option>
                        </select>
                    </div>
                </div>

                <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                    <div class="form-group" style="flex: 1;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">店主姓名</label>
                        <input type="text" id="shopOwner" value="${shopData.owner}" placeholder="输入店主姓名" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                    </div>
                    <div class="form-group" style="flex: 1;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">联系电话</label>
                        <input type="tel" id="shopPhone" value="${shopData.phone}" placeholder="输入联系电话" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                    </div>
                </div>

                <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                    <div class="form-group" style="flex: 1;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">营业时间</label>
                        <input type="text" id="shopHours" value="${shopData.businessHours}" placeholder="09:00-21:00" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                    </div>
                    <div class="form-group" style="flex: 1;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">经营状态</label>
                        <select id="shopStatus" style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                            <option value="operating" ${shopData.status === 'operating' ? 'selected' : ''}>🟢 正常营业</option>
                            <option value="renovation" ${shopData.status === 'renovation' ? 'selected' : ''}>🟡 装修中</option>
                            <option value="closed" ${shopData.status === 'closed' ? 'selected' : ''}>🔴 暂停营业</option>
                            <option value="vacant" ${shopData.status === 'vacant' ? 'selected' : ''}>⚫ 空置</option>
                        </select>
                    </div>
                </div>

                <div class="form-group" style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 4px; font-weight: 500;">月租金 (元)</label>
                    <input type="number" id="shopRent" value="${shopData.rent}" min="0" step="100" style="
                        width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                        font-size: 14px; box-sizing: border-box;
                    ">
                </div>

                <div class="form-group" style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 4px; font-weight: 500;">店铺描述</label>
                    <textarea id="shopDescription" rows="3" placeholder="输入店铺描述..." style="
                        width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                        font-size: 14px; resize: vertical; box-sizing: border-box;
                    ">${shopData.description}</textarea>
                </div>

                <div class="info-section" style="background: #f8fafc; padding: 12px; border-radius: 6px;">
                    <h4 style="margin: 0 0 8px 0; font-size: 14px; color: #374151;">区域信息</h4>
                    <div style="font-size: 12px; color: #6b7280;">
                        <div>形状: ${shopData.shape === 'polygon' ? '多边形' : shopData.shape === 'rectangle' ? '矩形' : shopData.shape}</div>
                        <div>面积: ${Math.round(shopData.area)} 平方像素</div>
                        <div>创建时间: ${new Date(shopData.createTime).toLocaleString()}</div>
                    </div>
                </div>
            </div>

            <div class="modal-actions" style="margin-top: 24px; text-align: right; display: flex; gap: 12px; justify-content: flex-end;">
                <button id="cancelShopBtn" style="
                    background: #6b7280; color: white; border: none; padding: 10px 20px;
                    border-radius: 6px; cursor: pointer; font-size: 14px;
                ">取消</button>
                <button id="confirmShopBtn" style="
                    background: #10b981; color: white; border: none; padding: 10px 20px;
                    border-radius: 6px; cursor: pointer; font-size: 14px;
                ">确认添加</button>
            </div>
        `;

        overlay.appendChild(modal);

        // 确保移除任何现有的配置对话框（包括防火分区的）
        const existingShopModals = document.querySelectorAll('.shop-config-modal');
        existingShopModals.forEach(modal => modal.remove());

        const existingFireZoneModals = document.querySelectorAll('.fire-zone-config-modal');
        existingFireZoneModals.forEach(modal => modal.remove());

        const existingModalOverlays = document.querySelectorAll('.modal-overlay');
        existingModalOverlays.forEach(modal => modal.remove());

        console.log('准备添加店铺配置对话框到DOM');
        console.log('对话框标题应该是: 🏪 店铺信息配置');
        console.log('对话框HTML内容:', modal.innerHTML.substring(0, 200) + '...');
        document.body.appendChild(overlay);
        console.log('店铺配置对话框已添加到DOM，元素:', overlay);
        console.log('对话框是否可见:', overlay.offsetWidth > 0 && overlay.offsetHeight > 0);

        // 验证对话框标题
        const titleElement = overlay.querySelector('h2');
        if (titleElement) {
            console.log('实际显示的标题:', titleElement.textContent);
        }

            this.bindShopConfigEvents(overlay, shopData, onConfirm);
        } catch (error) {
            console.error('显示店铺配置对话框时出错:', error);
            alert('显示配置对话框时出错，请查看控制台了解详情');
        }
    }

    /**
     * 绑定店铺配置事件
     */
    bindShopConfigEvents(overlay, shopData, onConfirm) {
        const confirmBtn = overlay.querySelector('#confirmShopBtn');
        const cancelBtn = overlay.querySelector('#cancelShopBtn');

        confirmBtn.addEventListener('click', () => {
            const configuredShop = {
                ...shopData,
                name: overlay.querySelector('#shopName').value.trim(),
                category: overlay.querySelector('#shopCategory').value,
                owner: overlay.querySelector('#shopOwner').value.trim(),
                phone: overlay.querySelector('#shopPhone').value.trim(),
                businessHours: overlay.querySelector('#shopHours').value.trim(),
                status: overlay.querySelector('#shopStatus').value,
                rent: parseFloat(overlay.querySelector('#shopRent').value) || 0,
                description: overlay.querySelector('#shopDescription').value.trim()
            };

            if (!configuredShop.name) {
                alert('请输入店铺名称');
                return;
            }

            overlay.remove();
            onConfirm(configuredShop);
        });

        cancelBtn.addEventListener('click', () => {
            overlay.remove();
        });

        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
            }
        });
    }

    /**
     * 渲染店铺
     */
    renderShop(shop) {
        const mapViewport = document.getElementById('mapViewport');
        if (!mapViewport) return;

        const shopElement = document.createElement('div');
        shopElement.className = 'shop-area';
        shopElement.id = `shop-${shop.id}`;

        if (shop.shape === 'rectangle') {
            this.renderRectangleShop(shopElement, shop);
        } else if (shop.shape === 'polygon') {
            this.renderPolygonShop(shopElement, shop);
        }

        mapViewport.appendChild(shopElement);
    }

    /**
     * 渲染矩形店铺
     */
    renderRectangleShop(element, shop) {
        const [p1, p2] = shop.points;
        const left = Math.min(p1.x, p2.x);
        const top = Math.min(p1.y, p2.y);
        const width = Math.abs(p2.x - p1.x);
        const height = Math.abs(p2.y - p1.y);

        element.style.cssText = `
            position: absolute;
            left: ${left}px;
            top: ${top}px;
            width: ${width}px;
            height: ${height}px;
            border: 2px solid ${this.getShopColor(shop.status)};
            background: ${this.getShopBackground(shop.status)};
            pointer-events: auto;
            cursor: pointer;
            z-index: 50;
        `;

        this.addShopLabel(element, shop);
    }

    /**
     * 渲染多边形店铺
     */
    renderPolygonShop(element, shop) {
        // 计算边界框
        const xs = shop.points.map(p => p.x);
        const ys = shop.points.map(p => p.y);
        const minX = Math.min(...xs);
        const minY = Math.min(...ys);
        const maxX = Math.max(...xs);
        const maxY = Math.max(...ys);

        element.style.cssText = `
            position: absolute;
            left: ${minX}px;
            top: ${minY}px;
            width: ${maxX - minX}px;
            height: ${maxY - minY}px;
            pointer-events: auto;
            cursor: pointer;
            z-index: 50;
        `;

        // 创建SVG多边形
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.style.cssText = 'width: 100%; height: 100%;';

        const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
        const points = shop.points.map(p => `${p.x - minX},${p.y - minY}`).join(' ');
        polygon.setAttribute('points', points);
        polygon.setAttribute('fill', this.getShopBackground(shop.status));
        polygon.setAttribute('stroke', this.getShopColor(shop.status));
        polygon.setAttribute('stroke-width', '2');

        svg.appendChild(polygon);
        element.appendChild(svg);

        this.addShopLabel(element, shop);
    }

    /**
     * 获取店铺颜色
     */
    getShopColor(status) {
        const colors = {
            operating: '#10b981',
            renovation: '#f59e0b',
            closed: '#ef4444',
            vacant: '#6b7280'
        };
        return colors[status] || '#10b981';
    }

    /**
     * 获取店铺背景色
     */
    getShopBackground(status) {
        const backgrounds = {
            operating: 'rgba(16, 185, 129, 0.1)',
            renovation: 'rgba(245, 158, 11, 0.1)',
            closed: 'rgba(239, 68, 68, 0.1)',
            vacant: 'rgba(107, 114, 128, 0.1)'
        };
        return backgrounds[status] || 'rgba(16, 185, 129, 0.1)';
    }

    /**
     * 添加店铺标签
     */
    addShopLabel(element, shop) {
        const label = document.createElement('div');
        label.style.cssText = `
            position: absolute;
            top: 4px;
            left: 4px;
            background: ${this.getShopColor(shop.status)};
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            pointer-events: none;
            max-width: calc(100% - 16px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        `;

        const categoryIcon = this.getCategoryIcon(shop.category);
        label.innerHTML = `${categoryIcon} ${shop.name}`;
        element.appendChild(label);

        // 添加点击事件
        element.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectShop(shop);
        });

        // 添加右键编辑事件
        element.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.showShopContextMenu(shop, e);
        });

        // 添加悬停提示
        element.addEventListener('mouseenter', () => {
            this.showShopTooltip(shop, element);
        });

        element.addEventListener('mouseleave', () => {
            this.hideShopTooltip();
        });
    }

    /**
     * 获取类别图标
     */
    getCategoryIcon(category) {
        const icons = {
            retail: '🛍️',
            restaurant: '🍽️',
            service: '🔧',
            entertainment: '🎮',
            education: '📚',
            other: '📦'
        };
        return icons[category] || '🏪';
    }

    /**
     * 选择店铺
     */
    selectShop(shop) {
        this.selectedShop = shop;
        showStatusMessage(`已选择店铺: ${shop.name}`);
        this.highlightShop(shop);
    }

    /**
     * 高亮显示店铺
     */
    highlightShop(shop) {
        // 清除之前的高亮
        document.querySelectorAll('.shop-area.highlighted').forEach(el => {
            el.classList.remove('highlighted');
            el.style.boxShadow = '';
        });

        // 高亮当前选中的店铺
        const shopElement = document.getElementById(`shop-${shop.id}`);
        if (shopElement) {
            shopElement.classList.add('highlighted');
            shopElement.style.boxShadow = '0 0 0 3px rgba(16, 185, 129, 0.5)';
        }
    }

    /**
     * 显示店铺右键菜单
     */
    showShopContextMenu(shop, e) {
        // 移除现有菜单
        const existingMenu = document.querySelector('.shop-context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }

        const menu = document.createElement('div');
        menu.className = 'shop-context-menu';
        menu.style.cssText = `
            position: fixed;
            left: ${e.clientX}px;
            top: ${e.clientY}px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            min-width: 160px;
            padding: 8px 0;
            font-size: 14px;
        `;

        const menuItems = [
            {
                icon: '✏️',
                text: '编辑店铺',
                action: () => this.editShop(shop)
            },
            {
                icon: '📋',
                text: '查看详情',
                action: () => this.showShopDetails(shop)
            },
            {
                icon: '💰',
                text: '财务信息',
                action: () => this.showFinancialInfo(shop)
            },
            {
                icon: '📞',
                text: '联系店主',
                action: () => this.contactOwner(shop)
            },
            { divider: true },
            {
                icon: '🗑️',
                text: '删除店铺',
                action: () => this.deleteShop(shop),
                danger: true
            }
        ];

        menuItems.forEach(item => {
            if (item.divider) {
                const divider = document.createElement('div');
                divider.style.cssText = `
                    height: 1px;
                    background: #e2e8f0;
                    margin: 4px 0;
                `;
                menu.appendChild(divider);
            } else {
                const menuItem = document.createElement('div');
                menuItem.style.cssText = `
                    padding: 8px 16px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    transition: background-color 0.15s ease;
                    ${item.danger ? 'color: #ef4444;' : ''}
                `;

                menuItem.innerHTML = `
                    <span>${item.icon}</span>
                    <span>${item.text}</span>
                `;

                menuItem.addEventListener('mouseenter', () => {
                    menuItem.style.backgroundColor = item.danger ? '#fef2f2' : '#f1f5f9';
                });

                menuItem.addEventListener('mouseleave', () => {
                    menuItem.style.backgroundColor = 'transparent';
                });

                menuItem.addEventListener('click', () => {
                    item.action();
                    menu.remove();
                });

                menu.appendChild(menuItem);
            }
        });

        document.body.appendChild(menu);

        // 调整菜单位置，防止超出屏幕
        const menuRect = menu.getBoundingClientRect();
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        if (menuRect.right > windowWidth) {
            menu.style.left = `${windowWidth - menuRect.width - 10}px`;
        }
        if (menuRect.bottom > windowHeight) {
            menu.style.top = `${windowHeight - menuRect.height - 10}px`;
        }

        // 点击其他地方关闭菜单
        setTimeout(() => {
            document.addEventListener('click', function closeMenu() {
                menu.remove();
                document.removeEventListener('click', closeMenu);
            });
        }, 0);
    }

    /**
     * 编辑店铺
     */
    editShop(shop) {
        this.showShopConfigDialog(shop, (updatedShop) => {
            // 更新店铺数据
            const index = this.shops.findIndex(s => s.id === shop.id);
            if (index !== -1) {
                this.shops[index] = updatedShop;
            }

            // 重新渲染店铺
            const shopElement = document.getElementById(`shop-${shop.id}`);
            if (shopElement) {
                shopElement.remove();
            }
            this.renderShop(updatedShop);

            // 保存更新
            this.saveShop(updatedShop);

            showStatusMessage(`店铺 ${updatedShop.name} 已更新`);
        });
    }

    /**
     * 显示店铺详情
     */
    showShopDetails(shop) {
        // TODO: 实现详情查看功能
        showStatusMessage(`查看店铺详情: ${shop.name}`);
    }

    /**
     * 显示财务信息
     */
    showFinancialInfo(shop) {
        // TODO: 实现财务信息功能
        showStatusMessage(`财务信息: ${shop.name}`);
    }

    /**
     * 联系店主
     */
    contactOwner(shop) {
        if (shop.phone) {
            showStatusMessage(`联系电话: ${shop.phone}`);
        } else {
            showStatusMessage('未设置联系电话');
        }
    }

    /**
     * 删除店铺
     */
    deleteShop(shop) {
        if (confirm(`确定要删除店铺 "${shop.name}" 吗？此操作不可恢复。`)) {
            // 从数组中移除
            this.shops = this.shops.filter(s => s.id !== shop.id);

            // 从DOM中移除
            const shopElement = document.getElementById(`shop-${shop.id}`);
            if (shopElement) {
                shopElement.remove();
            }

            // 从存储中删除
            // TODO: 实现从存储中删除

            showStatusMessage(`已删除店铺: ${shop.name}`);
        }
    }

    /**
     * 显示店铺提示框
     */
    showShopTooltip(shop, element) {
        // TODO: 实现店铺提示框
        console.log('显示店铺提示框:', shop);
    }

    /**
     * 隐藏店铺提示框
     */
    hideShopTooltip() {
        // TODO: 实现隐藏提示框
    }

    /**
     * 保存店铺到存储
     */
    async saveShop(shop) {
        try {
            if (this.mapController.storage) {
                // 这里可以扩展存储管理器来支持店铺数据
                console.log('保存店铺:', shop);
            }
        } catch (error) {
            console.error('保存店铺失败:', error);
            showStatusMessage('保存店铺失败', 3000);
        }
    }
}

export default ShopManager;
