/**
 * data-manager.js - 数据管理模块
 * 提供数据导出、导入、备份和恢复功能
 */

import { showStatusMessage } from './ui-utils.js';

class DataManager {
    constructor(storageManager) {
        this.storage = storageManager;
    }

    /**
     * 导出所有数据为JSON文件
     */
    async exportData() {
        try {
            showStatusMessage('正在导出数据...');
            
            const exportData = await this.storage.exportAllData();
            if (!exportData) {
                throw new Error('导出数据失败');
            }

            // 创建下载链接
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            // 创建下载链接
            const link = document.createElement('a');
            link.href = url;
            link.download = `monitor-system-backup-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 清理URL对象
            URL.revokeObjectURL(url);

            showStatusMessage('数据导出成功');
            return true;
        } catch (error) {
            console.error('导出数据失败:', error);
            showStatusMessage(`导出失败: ${error.message}`, 5000);
            return false;
        }
    }

    /**
     * 从JSON文件导入数据
     */
    async importData(file) {
        try {
            showStatusMessage('正在导入数据...');

            if (!file) {
                throw new Error('请选择要导入的文件');
            }

            if (!file.name.endsWith('.json')) {
                throw new Error('请选择JSON格式的备份文件');
            }

            // 读取文件内容
            const fileContent = await this.readFileAsText(file);
            const importData = JSON.parse(fileContent);

            // 验证数据格式
            if (!this.validateImportData(importData)) {
                throw new Error('备份文件格式不正确');
            }

            // 确认导入操作
            const confirmed = confirm(
                `确定要导入备份数据吗？\n` +
                `备份时间: ${importData.timestamp}\n` +
                `版本: ${importData.version}\n` +
                `注意: 这将覆盖当前所有数据！`
            );

            if (!confirmed) {
                showStatusMessage('导入操作已取消');
                return false;
            }

            // 执行导入
            const success = await this.storage.importAllData(importData);
            if (success) {
                showStatusMessage('数据导入成功，页面将刷新');
                // 延迟刷新页面以应用新数据
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
                return true;
            } else {
                throw new Error('导入数据到数据库失败');
            }
        } catch (error) {
            console.error('导入数据失败:', error);
            showStatusMessage(`导入失败: ${error.message}`, 5000);
            return false;
        }
    }

    /**
     * 清空所有数据
     */
    async clearAllData() {
        try {
            const confirmed = confirm(
                '确定要清空所有数据吗？\n' +
                '这将删除所有楼层、设备、设置和系统状态数据！\n' +
                '此操作不可恢复！'
            );

            if (!confirmed) {
                showStatusMessage('清空操作已取消');
                return false;
            }

            showStatusMessage('正在清空数据...');
            await this.storage.clearAllData();
            
            // 清空localStorage中的认证信息
            localStorage.clear();
            
            showStatusMessage('数据清空成功，页面将刷新');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
            
            return true;
        } catch (error) {
            console.error('清空数据失败:', error);
            showStatusMessage(`清空失败: ${error.message}`, 5000);
            return false;
        }
    }

    /**
     * 创建数据备份
     */
    async createBackup() {
        try {
            showStatusMessage('正在创建备份...');
            const success = await this.storage.createBackup();
            if (success) {
                showStatusMessage('备份创建成功');
            } else {
                throw new Error('备份创建失败');
            }
            return success;
        } catch (error) {
            console.error('创建备份失败:', error);
            showStatusMessage(`备份失败: ${error.message}`, 5000);
            return false;
        }
    }

    /**
     * 显示数据管理界面
     */
    showDataManagementUI() {
        // 移除现有的数据管理界面
        const existingUI = document.querySelector('.data-management-modal');
        if (existingUI) {
            existingUI.remove();
        }

        // 创建模态框
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay data-management-modal';
        overlay.style.cssText = `
            display: flex !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(0,0,0,0.5) !important;
            z-index: 1000 !important;
            justify-content: center !important;
            align-items: center !important;
        `;

        const modal = document.createElement('div');
        modal.className = 'data-management-content';
        modal.style.cssText = `
            background: #fff !important;
            padding: 24px !important;
            border-radius: 8px !important;
            min-width: 500px !important;
            max-width: 600px !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3) !important;
            position: relative !important;
        `;

        modal.innerHTML = `
            <div class="modal-header" style="margin-bottom: 20px;">
                <h2 style="margin: 0; color: #333;">数据管理</h2>
                <p style="margin: 8px 0 0 0; color: #666; font-size: 14px;">
                    管理系统数据的导出、导入和备份
                </p>
            </div>
            
            <div class="modal-body">
                <div class="data-action-group" style="margin-bottom: 20px;">
                    <h3 style="margin: 0 0 12px 0; font-size: 16px; color: #333;">数据备份</h3>
                    <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                        <button id="exportDataBtn" class="data-btn export-btn" style="
                            background: #10b981; color: white; border: none; padding: 10px 16px;
                            border-radius: 6px; cursor: pointer; font-size: 14px;
                        ">导出数据</button>
                        <button id="createBackupBtn" class="data-btn backup-btn" style="
                            background: #3b82f6; color: white; border: none; padding: 10px 16px;
                            border-radius: 6px; cursor: pointer; font-size: 14px;
                        ">创建备份</button>
                    </div>
                </div>
                
                <div class="data-action-group" style="margin-bottom: 20px;">
                    <h3 style="margin: 0 0 12px 0; font-size: 16px; color: #333;">数据恢复</h3>
                    <div style="display: flex; gap: 12px; align-items: center; flex-wrap: wrap;">
                        <input type="file" id="importFileInput" accept=".json" style="
                            padding: 8px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; flex: 1; min-width: 200px;
                        ">
                        <button id="importDataBtn" class="data-btn import-btn" style="
                            background: #f59e0b; color: white; border: none; padding: 10px 16px;
                            border-radius: 6px; cursor: pointer; font-size: 14px;
                        ">导入数据</button>
                    </div>
                </div>
                
                <div class="data-action-group">
                    <h3 style="margin: 0 0 12px 0; font-size: 16px; color: #333;">危险操作</h3>
                    <button id="clearDataBtn" class="data-btn clear-btn" style="
                        background: #ef4444; color: white; border: none; padding: 10px 16px;
                        border-radius: 6px; cursor: pointer; font-size: 14px;
                    ">清空所有数据</button>
                    <p style="margin: 8px 0 0 0; color: #666; font-size: 12px;">
                        ⚠️ 此操作将删除所有数据且不可恢复
                    </p>
                </div>
            </div>
            
            <div class="modal-actions" style="margin-top: 24px; text-align: right;">
                <button id="closeDataManagementBtn" style="
                    background: #6b7280; color: white; border: none; padding: 10px 20px;
                    border-radius: 6px; cursor: pointer; font-size: 14px;
                ">关闭</button>
            </div>
        `;

        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // 绑定事件
        this.bindDataManagementEvents(overlay);
    }

    /**
     * 绑定数据管理界面事件
     */
    bindDataManagementEvents(overlay) {
        const exportBtn = overlay.querySelector('#exportDataBtn');
        const importBtn = overlay.querySelector('#importDataBtn');
        const clearBtn = overlay.querySelector('#clearDataBtn');
        const backupBtn = overlay.querySelector('#createBackupBtn');
        const closeBtn = overlay.querySelector('#closeDataManagementBtn');
        const fileInput = overlay.querySelector('#importFileInput');

        exportBtn.addEventListener('click', () => this.exportData());
        
        importBtn.addEventListener('click', () => {
            const file = fileInput.files[0];
            this.importData(file);
        });
        
        clearBtn.addEventListener('click', () => this.clearAllData());
        
        backupBtn.addEventListener('click', () => this.createBackup());
        
        closeBtn.addEventListener('click', () => overlay.remove());
        
        // 点击背景关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
            }
        });
    }

    /**
     * 读取文件内容为文本
     */
    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('文件读取失败'));
            reader.readAsText(file);
        });
    }

    /**
     * 验证导入数据格式
     */
    validateImportData(data) {
        return (
            data &&
            typeof data === 'object' &&
            data.version &&
            data.timestamp &&
            data.data &&
            typeof data.data === 'object'
        );
    }
}

export default DataManager;
