/**
 * ui-utils.js - UI工具模块
 * 提供通用的UI操作函数
 */

// 工具提示元素
let tooltip = null;

// 状态指示器元素
let statusIndicator = null;

// 状态指示器定时器
let statusTimeout = null;

/**
 * 显示工具提示
 * @param {MouseEvent} event - 鼠标事件
 * @param {string} content - 提示内容
 */
export function showTooltip(event, content) {
    // 如果工具提示不存在，则创建
    if (!tooltip) {
        tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        document.body.appendChild(tooltip);
    }
    
    // 设置内容
    tooltip.innerHTML = content;
    
    // 计算位置
    const x = event.clientX;
    const y = event.clientY;
    
    // 获取工具提示尺寸
    const rect = tooltip.getBoundingClientRect();
    const tooltipWidth = rect.width;
    const tooltipHeight = rect.height;
    
    // 获取视口尺寸
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    // 计算最佳位置（避免超出视口）
    let left = x + 15;
    let top = y + 15;
    
    // 如果右侧空间不足，则显示在左侧
    if (left + tooltipWidth > viewportWidth - 10) {
        left = x - tooltipWidth - 15;
    }
    
    // 如果下方空间不足，则显示在上方
    if (top + tooltipHeight > viewportHeight - 10) {
        top = y - tooltipHeight - 15;
    }
    
    // 确保不超出左侧和顶部
    left = Math.max(10, left);
    top = Math.max(10, top);
    
    // 设置位置
    tooltip.style.left = `${left}px`;
    tooltip.style.top = `${top}px`;
    
    // 显示工具提示
    setTimeout(() => {
        tooltip.classList.add('show');
    }, 10);
}

/**
 * 隐藏工具提示
 */
export function hideTooltip() {
    if (tooltip) {
        tooltip.classList.remove('show');
    }
}

/**
 * 显示状态消息
 * @param {string} message - 状态消息
 * @param {number} duration - 显示时长（毫秒）
 */
export function showStatusMessage(message, duration = 2000) {
    // 如果状态指示器不存在，则创建
    if (!statusIndicator) {
        statusIndicator = document.createElement('div');
        statusIndicator.className = 'status-indicator';
        document.body.appendChild(statusIndicator);
    }
    
    // 清除之前的定时器
    if (statusTimeout) {
        clearTimeout(statusTimeout);
    }
    
    // 设置消息
    statusIndicator.textContent = message;
    
    // 显示状态指示器
    statusIndicator.classList.add('show');
    
    // 设置定时器隐藏
    statusTimeout = setTimeout(() => {
        statusIndicator.classList.remove('show');
    }, duration);
}

/**
 * 更新统计信息
 * @param {Object} statistics - 统计数据
 */
export function updateStatistics(statistics) {
    // 更新总数
    const totalElement = document.getElementById('statTotal');
    if (totalElement) {
        animateNumber(totalElement, statistics.total);
    }
    
    // 更新正常数
    const normalElement = document.getElementById('statNormal');
    if (normalElement) {
        animateNumber(normalElement, statistics.normal);
    }
    
    // 更新警告数
    const warningElement = document.getElementById('statWarning');
    if (warningElement) {
        animateNumber(warningElement, statistics.warning);
    }
    
    // 更新错误数
    const errorElement = document.getElementById('statError');
    if (errorElement) {
        animateNumber(errorElement, statistics.error);
    }
}

/**
 * 数字动画
 * @param {HTMLElement} element - 目标元素
 * @param {number} newValue - 新值
 * @param {number} duration - 动画时长（毫秒）
 */
function animateNumber(element, newValue, duration = 500) {
    const startValue = parseInt(element.textContent) || 0;
    const startTime = performance.now();
    
    function updateNumber(timestamp) {
        const elapsed = timestamp - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // 使用缓动函数使动画更自然
        const easedProgress = easeOutQuad(progress);
        
        // 计算当前值
        const currentValue = Math.floor(startValue + (newValue - startValue) * easedProgress);
        
        // 更新元素内容
        element.textContent = currentValue;
        
        // 如果动画未完成，继续更新
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        } else {
            // 确保最终值正确
            element.textContent = newValue;
        }
    }
    
    requestAnimationFrame(updateNumber);
}

/**
 * 二次方缓出函数
 * @param {number} t - 进度（0-1）
 * @returns {number} 缓动后的进度
 */
function easeOutQuad(t) {
    return t * (2 - t);
}