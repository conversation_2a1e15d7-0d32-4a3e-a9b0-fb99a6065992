/**
 * main.js - 主程序入口
 * 负责初始化和协调各个模块
 */

import CONFIG from './config.js';
import AuthManager from './auth-manager.js';
import MapController from './map-controller.js';
import DeviceManager from './device-manager.js';
import DataManager from './data-manager.js';
import ContextMenuManager from './context-menu-manager.js';
import { showStatusMessage } from './ui-utils.js';

// 全局实例
let authManager;
let mapController;
let deviceManager;
let dataManager;
let contextMenuManager;

/**
 * 初始化应用
 */
async function initApp() {
    // 显示加载状态
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'flex';
    }

    try {
        // 初始化认证管理器
        authManager = new AuthManager();

        // 初始化地图控制器
        mapController = new MapController();

        // 初始化设备管理器
        deviceManager = new DeviceManager();

        // 初始化数据管理器
        dataManager = new DataManager(mapController.storage);

        // 初始化右键菜单管理器
        contextMenuManager = new ContextMenuManager(mapController, deviceManager);

        // 初始化存储并加载保存的数据
        if (mapController.storage) {
            await mapController.storage.init();

            // 加载保存的楼层数据
            const savedFloors = await mapController.storage.getFloorPlans();
            if (savedFloors && savedFloors.length > 0) {
                // 使用保存的楼层数据，保持原有格式
                CONFIG.MOCK.FLOORS = savedFloors.map(floor => ({
                    id: floor.id,
                    name: floor.name,
                    description: floor.description || '',
                    planUrl: floor.planUrl || null,
                    deviceCount: floor.deviceCount || 150,
                    timestamp: floor.timestamp
                }));
                console.log(`已加载${savedFloors.length}个保存的楼层`);
            } else {
                // 如果没有保存的楼层数据，保存默认楼层到数据库
                console.log('没有找到保存的楼层数据，保存默认楼层');
                for (const floor of CONFIG.MOCK.FLOORS) {
                    const floorData = {
                        ...floor,
                        timestamp: Date.now()
                    };
                    await mapController.storage.saveFloorPlan(floorData);
                }
            }

            // 加载用户设置
            await loadUserSettings();

            // 加载系统状态
            await loadSystemState();
        }

        // 生成设备点
        deviceManager.generateDevices(150);

        // 初始化楼层选择
        initFloorSelection();

        // 初始化浮动按钮
        initFloatingButtons();

        // 初始化数据管理按钮
        initDataManagementButton();

        // 默认选中第一个楼层
        const firstFloor = document.querySelector('.floor-item:not(.add-floor-btn)');
        if (firstFloor) {
            firstFloor.click();
        }
    } catch (error) {
        console.error('初始化应用失败:', error);
        showStatusMessage('初始化失败: ' + error.message, 5000);
    } finally {
        // 隐藏加载状态
        if (loadingElement) {
            setTimeout(() => {
                loadingElement.style.display = 'none';
                showStatusMessage('系统初始化完成');
            }, 500);
        }
    }

    // 显示版本信息
    const versionElement = document.getElementById('versionInfo');
    if (versionElement) {
        versionElement.textContent = CONFIG.SYSTEM.VERSION;
    }
}

/**
 * 加载用户设置
 */
async function loadUserSettings() {
    if (!mapController.storage) return;

    try {
        // 加载地图缩放级别
        const savedZoom = await mapController.storage.getSetting('mapZoom', CONFIG.MAP.DEFAULT_ZOOM);
        if (mapController.zoom !== undefined) {
            mapController.zoom = savedZoom;
        }

        // 加载地图位置
        const savedPanX = await mapController.storage.getSetting('mapPanX', 0);
        const savedPanY = await mapController.storage.getSetting('mapPanY', 0);
        if (mapController.panX !== undefined && mapController.panY !== undefined) {
            mapController.panX = savedPanX;
            mapController.panY = savedPanY;
        }

        // 加载最后选中的楼层
        const lastSelectedFloor = await mapController.storage.getSetting('lastSelectedFloor', null);
        if (lastSelectedFloor) {
            // 在楼层列表初始化后设置选中状态
            setTimeout(() => {
                const floorElement = document.querySelector(`[data-id="${lastSelectedFloor}"]`);
                if (floorElement) {
                    floorElement.click();
                }
            }, 100);
        }

        console.log('用户设置加载完成');
    } catch (error) {
        console.error('加载用户设置失败:', error);
    }
}

/**
 * 加载系统状态
 */
async function loadSystemState() {
    if (!mapController.storage) return;

    try {
        // 加载设备统计信息
        const savedStats = await mapController.storage.getSystemState('deviceStats', null);
        if (savedStats && deviceManager) {
            deviceManager.statistics = savedStats;
        }

        // 加载搜索历史
        const searchHistory = await mapController.storage.getSystemState('searchHistory', []);
        if (searchHistory.length > 0) {
            // 可以在这里恢复搜索历史到搜索框的下拉列表
            console.log('搜索历史:', searchHistory);
        }

        console.log('系统状态加载完成');
    } catch (error) {
        console.error('加载系统状态失败:', error);
    }
}

/**
 * 保存用户设置
 */
async function saveUserSettings() {
    if (!mapController.storage) return;

    try {
        // 保存地图状态
        if (mapController.zoom !== undefined) {
            await mapController.storage.saveSetting('mapZoom', mapController.zoom);
        }
        if (mapController.panX !== undefined && mapController.panY !== undefined) {
            await mapController.storage.saveSetting('mapPanX', mapController.panX);
            await mapController.storage.saveSetting('mapPanY', mapController.panY);
        }

        // 保存当前选中的楼层
        const activeFloor = document.querySelector('.floor-item.active');
        if (activeFloor && activeFloor.dataset.id) {
            await mapController.storage.saveSetting('lastSelectedFloor', activeFloor.dataset.id);
        }

        console.log('用户设置保存完成');
    } catch (error) {
        console.error('保存用户设置失败:', error);
    }
}

/**
 * 保存系统状态
 */
async function saveSystemState() {
    if (!mapController.storage) return;

    try {
        // 保存设备统计信息
        if (deviceManager && deviceManager.statistics) {
            await mapController.storage.saveSystemState('deviceStats', deviceManager.statistics);
        }

        console.log('系统状态保存完成');
    } catch (error) {
        console.error('保存系统状态失败:', error);
    }
}

/**
 * 初始化楼层选择
 */
function initFloorSelection() {
    const floorList = document.getElementById('floorList');
    if (!floorList) return;

    // 清空现有内容
    floorList.innerHTML = '';

    // 添加"+"按钮（先移除已存在的）
    const titleRow = document.querySelector('.floor-title-row');
    if (titleRow) {
        const existingAddBtn = titleRow.querySelector('.add-floor-btn');
        if (existingAddBtn) {
            titleRow.removeChild(existingAddBtn);
        }

        const addButton = document.createElement('div');
        addButton.className = 'floor-item add-floor-btn';
        addButton.innerHTML = '+';
        addButton.title = '添加新楼层';
        addButton.addEventListener('click', (e) => {
            e.stopPropagation();
            showFloorEditModal(null);
        });
        titleRow.appendChild(addButton);
    }

    // 添加楼层项
    CONFIG.MOCK.FLOORS.forEach(floor => {
        const floorItem = document.createElement('div');
        floorItem.className = 'floor-item';
        floorItem.dataset.id = floor.id;

        floorItem.innerHTML = `
            <span class="floor-name">${floor.name}</span>
        `;

        // 点击事件
        floorItem.addEventListener('click', () => {
            // 移除其他楼层的活动状态
            document.querySelectorAll('.floor-item').forEach(item => {
                item.classList.remove('active');
            });

            // 添加当前楼层的活动状态
            floorItem.classList.add('active');

            // 重新生成设备点
            deviceManager.generateDevices(150); // 使用固定设备数量

            // 显示楼层平面图
            if (floor.planUrl) {
                mapController.showFloorPlan(floor.planUrl);
            } else if (floor.base64) {
                // 兼容旧数据格式
                const mimeType = floor.fileType || 'image/png';
                const dataUrl = `data:${mimeType};base64,${floor.base64}`;
                mapController.showFloorPlan(dataUrl);
            }

            // 显示状态消息
            showStatusMessage(`已切换到${floor.name}`);
        });

        // 右键菜单
        floorItem.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            showFloorContextMenu(e, floor);
        });

        floorList.appendChild(floorItem);
    });

    // 默认选中第一个楼层
    const firstFloor = floorList.querySelector('.floor-item:not(.add-floor-btn)');
    if (firstFloor) {
        firstFloor.classList.add('active');
    }
}

/**
 * 显示楼层右键菜单
 */
function showFloorContextMenu(e, floor) {
    // 移除现有菜单
    const existingMenu = document.querySelector('.floor-context-menu');
    if (existingMenu) existingMenu.remove();

    // 创建菜单
    const menu = document.createElement('div');
    menu.className = 'floor-context-menu';
    menu.style.left = `${e.clientX}px`;
    menu.style.top = `${e.clientY}px`;

    // 添加菜单项
    const editOption = document.createElement('div');
    editOption.className = 'menu-option';
    editOption.textContent = '编辑';
    editOption.addEventListener('click', () => {
        showFloorEditModal(floor);
        menu.remove();
    });

    const deleteOption = document.createElement('div');
    deleteOption.className = 'menu-option';
    deleteOption.textContent = '删除';
    deleteOption.addEventListener('click', async () => {
        if (confirm(`确定要删除楼层 ${floor.name} 吗？`)) {
            try {
                // 从IndexedDB中删除楼层
                if (mapController.storage && mapController.storage.deleteFloorPlan) {
                    await mapController.storage.deleteFloorPlan(floor.id);
                }

                // 从内存中删除楼层
                CONFIG.MOCK.FLOORS = CONFIG.MOCK.FLOORS.filter(f => f.id !== floor.id);

                // 重新初始化楼层选择
                initFloorSelection();

                // 显示状态消息
                showStatusMessage(`已删除楼层 ${floor.name}`);

                // 触发自定义事件，通知其他组件更新
                document.dispatchEvent(new CustomEvent('floorsUpdated'));
            } catch (error) {
                console.error('删除楼层失败:', error);
                showStatusMessage(`删除失败: ${error.message}`, 5000);
            }
        }
        menu.remove();
    });

    menu.appendChild(editOption);
    menu.appendChild(deleteOption);
    document.body.appendChild(menu);

    // 点击其他地方关闭菜单
    const closeMenu = (e) => {
        if (!menu.contains(e.target)) {
            menu.remove();
            document.removeEventListener('click', closeMenu);
        }
    };
    document.addEventListener('click', closeMenu);
}

/**
 * 显示楼层编辑弹窗
 */
function showFloorEditModal(floor) {
    console.log('showFloorEditModal called with:', floor);

    console.log('Initializing modal overlay...');
    let overlay = document.querySelector('.modal-overlay');
    if (!overlay) {
        console.log('Creating new modal overlay...');
        overlay = document.createElement('div');
        overlay.className = 'modal-overlay';
        overlay.id = 'modalOverlay';
        overlay.style.cssText = `
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        `;
        document.body.appendChild(overlay);
        console.log('Modal overlay created:', overlay);
    }

    console.log('Creating modal content...');

    // 完全移除现有modal
    document.querySelectorAll('.modal-overlay, .floor-edit-modal').forEach(el => el.remove());

    // 重用已存在的overlay变量
    overlay = document.createElement('div');
    overlay.id = 'modalOverlay';
    overlay.style.cssText = `
        display: flex !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background-color: rgba(120,120,120,0.2) !important;
        z-index: 1000 !important;
        justify-content: center !important;
        align-items: center !important;
    `;
    console.log('Overlay style enforced:', overlay.style.cssText);

    // 创建modal内容
    const modal = document.createElement('div');
    modal.className = 'floor-edit-modal';
    modal.style.cssText = `
        background: #fff !important;
        padding: 20px !important;
        border-radius: 8px !important;
        min-width: 400px !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2) !important;
        z-index: 1001 !important;
        position: relative !important;
        border: 1px solid #ccc !important;
    `;
    // 构建modal内容
    const modalHTML = `
        <div class="modal-header">
            <h2 class="modal-title">${floor ? '编辑楼层' : '添加楼层'}</h2>
        </div>
        <div class="modal-body">
            <form id="floorEditForm">
                <div class="form-group">
                    <label class="form-label">楼层名称</label>
                    <input type="text" class="form-input" id="floorName"
                           value="${floor ? floor.name : ''}" required>
                </div>
                <div class="form-group">
                    <label class="form-label">楼层描述</label>
                    <textarea class="form-input" id="floorDescription"
                           rows="3">${floor ? floor.description || '' : ''}</textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">楼层平面图</label>
                    <input type="file" class="form-input" id="floorPlan" accept="image/*">
                    <small style="color: #666; font-size: 12px; margin-top: 4px; display: block;">
                        支持 JPG、PNG、GIF 等图片格式，建议大小不超过 5MB
                    </small>
                    ${floor && (floor.planUrl || floor.base64) ?
                        `<div class="current-plan" style="margin-top: 8px; padding: 8px; background: #f0f0f0; border-radius: 4px; font-size: 12px;">
                            ✅ 当前已有平面图，选择新文件可替换
                        </div>` : ''}
                </div>
            </form>
        </div>
        <div class="modal-actions">
            <button class="modal-btn btn-cancel" id="floorEditCancel">取消</button>
            <button class="modal-btn btn-confirm" id="floorEditConfirm">确认</button>
        </div>
    `;
    modal.innerHTML = modalHTML;

    // 强制添加到body最上层
    document.body.insertAdjacentElement('afterbegin', overlay);
    overlay.insertAdjacentElement('afterbegin', modal);

    console.log('Modal fully initialized:', {
        overlay: overlay,
        modal: modal,
        overlayStyle: window.getComputedStyle(overlay),
        modalStyle: window.getComputedStyle(modal)
    });

    // 调试父元素可见性
    console.log('Parent visibility check:');
    console.log('document.body.style.overflow:', document.body.style.overflow);
    console.log('document.documentElement.style.overflow:', document.documentElement.style.overflow);
    console.log('overlay parent visibility:', window.getComputedStyle(overlay.parentElement).visibility);

    // 调试z-index层级
    console.log('Z-index hierarchy:');
    console.log('overlay z-index:', window.getComputedStyle(overlay).zIndex);
    console.log('modal z-index:', window.getComputedStyle(modal).zIndex);

    console.log('Debug styles applied');

    // 绑定确认按钮事件
    const confirmBtn = modal.querySelector('#floorEditConfirm');
    confirmBtn.addEventListener('click', () => {
        const name = modal.querySelector('#floorName').value.trim();
        const description = modal.querySelector('#floorDescription').value.trim();
        const planFile = modal.querySelector('#floorPlan').files[0];

        if (!name) {
            alert('请输入楼层名称');
            return;
        }

        const saveFloor = async () => {
            try {
                let base64Data = null;
                let finalPlanUrl = null;

                // 如果有新上传的文件，转换为Base64
                if (planFile) {
                    base64Data = await fileToBase64(planFile);
                    finalPlanUrl = `data:${planFile.type};base64,${base64Data}`;
                }

                if (floor) {
                    // 更新现有楼层
                    floor.name = name;
                    floor.description = description;
                    if (finalPlanUrl) {
                        floor.planUrl = finalPlanUrl;
                        floor.base64 = base64Data;
                        floor.fileType = planFile.type;
                    }

                    // 保存到IndexedDB
                    if (mapController.storage) {
                        await mapController.storage.saveFloorPlan(floor);
                    }

                    showStatusMessage(`已更新楼层 ${name}`);
                } else {
                    // 添加新楼层
                    const newFloor = {
                        id: `floor-${Date.now()}`,
                        name,
                        description,
                        planUrl: finalPlanUrl,
                        base64: base64Data,
                        fileType: planFile ? planFile.type : null,
                        timestamp: Date.now()
                    };
                    CONFIG.MOCK.FLOORS.push(newFloor);

                    // 保存到IndexedDB
                    if (mapController.storage) {
                        await mapController.storage.saveFloorPlan(newFloor);
                    }

                    showStatusMessage(`已添加楼层 ${name}`);
                }

                initFloorSelection();
                overlay.style.display = 'none';

                // 触发自定义事件，通知其他组件更新
                document.dispatchEvent(new CustomEvent('floorsUpdated'));
            } catch (error) {
                console.error('处理平面图文件失败:', error);
                showStatusMessage(`保存失败: ${error.message}`, 5000);
            }
        };

        // 执行保存
        saveFloor().catch(error => {
            console.error('保存楼层数据失败:', error);
            showStatusMessage(`保存失败: ${error.message}`, 5000);
        });
    });

    // 绑定取消按钮事件
    const cancelBtn = modal.querySelector('#floorEditCancel');
    cancelBtn.addEventListener('click', () => {
        overlay.style.display = 'none';
    });
}

/**
 * 初始化浮动按钮
 */
function initFloatingButtons() {
    // 刷新按钮
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
            // 获取当前选中的楼层
            const activeFloor = document.querySelector('.floor-item.active');
            let deviceCount = 150; // 默认设备数量

            if (activeFloor) {
                const floorId = activeFloor.dataset.id;
                const floor = CONFIG.MOCK.FLOORS.find(f => f.id === floorId);
                if (floor && floor.deviceCount) {
                    deviceCount = floor.deviceCount;
                }
            }

            // 重新生成设备点
            deviceManager.generateDevices(deviceCount);

            // 显示状态消息
            showStatusMessage('已刷新设备状态');
        });
    }

    // 管理员按钮
    const adminBtn = document.getElementById('adminBtn');
    if (adminBtn && authManager) {
        adminBtn.addEventListener('click', () => {
            authManager.openAdminWindow();
        });
    }
}

/**
 * 将文件转换为Base64字符串
 * @param {File} file - 要转换的文件
 * @returns {Promise<string>} Base64字符串（不包含data:前缀）
 */
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
            // 移除data:前缀，只保留Base64数据
            const base64 = reader.result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = (error) => {
            console.error('文件读取失败:', error);
            reject(new Error('文件读取失败'));
        };
        reader.readAsDataURL(file);
    });
}

/**
 * 初始化数据管理按钮
 */
function initDataManagementButton() {
    const dataManagementBtn = document.getElementById('dataManagementBtn');
    if (dataManagementBtn && dataManager) {
        dataManagementBtn.addEventListener('click', () => {
            dataManager.showDataManagementUI();
        });
    }
}

// 当DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', initApp);

// 页面卸载时保存用户设置
window.addEventListener('beforeunload', () => {
    // 保存用户设置和系统状态
    saveUserSettings();
    saveSystemState();
});

// 定期保存用户设置（每30秒）
setInterval(() => {
    saveUserSettings();
    saveSystemState();
}, 30000);

// 监听楼层切换事件，保存当前选中的楼层
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('floor-item') && !e.target.classList.contains('add-floor-btn')) {
        // 延迟保存，确保状态已更新
        setTimeout(() => {
            saveUserSettings();
        }, 100);
    }
});

// 监听地图操作事件，保存地图状态
document.addEventListener('wheel', () => {
    // 防抖保存地图状态
    clearTimeout(window.mapSaveTimeout);
    window.mapSaveTimeout = setTimeout(() => {
        saveUserSettings();
    }, 1000);
});

document.addEventListener('mouseup', () => {
    // 防抖保存地图状态
    clearTimeout(window.mapSaveTimeout);
    window.mapSaveTimeout = setTimeout(() => {
        saveUserSettings();
    }, 1000);
});
