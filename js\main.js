/**
 * main.js - 主程序入口
 * 负责初始化和协调各个模块
 */

import CONFIG from './config.js';
import AuthManager from './auth-manager.js';
import MapController from './map-controller.js';
import DeviceManager from './device-manager.js';
import { showStatusMessage } from './ui-utils.js';

// 全局实例
let authManager;
let mapController;
let deviceManager;

/**
 * 初始化应用
 */
async function initApp() {
    // 显示加载状态
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'flex';
    }
    
    try {
        // 初始化认证管理器
        authManager = new AuthManager();
        
        // 初始化地图控制器
        mapController = new MapController();
        
        // 初始化设备管理器
        deviceManager = new DeviceManager();
        
        // 生成设备点
        deviceManager.generateDevices(150);
        
        // 初始化存储
        if (mapController.storage) {
            await mapController.storage.init();
            // 检查是否有保存的楼层数据
            const savedFloors = await mapController.storage.getFloorPlans();
            if (savedFloors && savedFloors.length > 0) {
                // 使用保存的楼层数据
                CONFIG.MOCK.FLOORS = savedFloors;
            }
        }
        
        // 初始化楼层选择
        initFloorSelection();
        
        // 初始化浮动按钮
        initFloatingButtons();
        
        // 默认选中第一个楼层
        const firstFloor = document.querySelector('.floor-item:not(.add-floor-btn)');
        if (firstFloor) {
            firstFloor.click();
        }
    } catch (error) {
        console.error('初始化应用失败:', error);
        showStatusMessage('初始化失败: ' + error.message, 5000);
    } finally {
        // 隐藏加载状态
        if (loadingElement) {
            setTimeout(() => {
                loadingElement.style.display = 'none';
                showStatusMessage('系统初始化完成');
            }, 500);
        }
    }
    
    // 显示版本信息
    const versionElement = document.getElementById('versionInfo');
    if (versionElement) {
        versionElement.textContent = CONFIG.SYSTEM.VERSION;
    }
}

/**
 * 初始化楼层选择
 */
function initFloorSelection() {
    const floorList = document.getElementById('floorList');
    if (!floorList) return;
    
    // 清空现有内容
    floorList.innerHTML = '';
    
    // 添加"+"按钮（先移除已存在的）
    const titleRow = document.querySelector('.floor-title-row');
    if (titleRow) {
        const existingAddBtn = titleRow.querySelector('.add-floor-btn');
        if (existingAddBtn) {
            titleRow.removeChild(existingAddBtn);
        }
        
        const addButton = document.createElement('div');
        addButton.className = 'floor-item add-floor-btn';
        addButton.innerHTML = '+';
        addButton.title = '添加新楼层';
        addButton.addEventListener('click', (e) => {
            e.stopPropagation();
            showFloorEditModal(null);
        });
        titleRow.appendChild(addButton);
    }
    
    // 添加楼层项
    CONFIG.MOCK.FLOORS.forEach(floor => {
        const floorItem = document.createElement('div');
        floorItem.className = 'floor-item';
        floorItem.dataset.id = floor.id;
        
        floorItem.innerHTML = `
            <span class="floor-name">${floor.name}</span>
        `;
        
        // 点击事件
        floorItem.addEventListener('click', () => {
            // 移除其他楼层的活动状态
            document.querySelectorAll('.floor-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 添加当前楼层的活动状态
            floorItem.classList.add('active');
            
            // 重新生成设备点
            deviceManager.generateDevices(150); // 使用固定设备数量
            
            // 显示楼层平面图
            if (floor.planUrl) {
                mapController.showFloorPlan(floor.planUrl);
            }
            
            // 显示状态消息
            showStatusMessage(`已切换到${floor.name}`);
        });
        
        // 右键菜单
        floorItem.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            showFloorContextMenu(e, floor);
        });
        
        floorList.appendChild(floorItem);
    });
    
    // 默认选中第一个楼层
    const firstFloor = floorList.querySelector('.floor-item:not(.add-floor-btn)');
    if (firstFloor) {
        firstFloor.classList.add('active');
    }
}

/**
 * 显示楼层右键菜单
 */
function showFloorContextMenu(e, floor) {
    // 移除现有菜单
    const existingMenu = document.querySelector('.floor-context-menu');
    if (existingMenu) existingMenu.remove();
    
    // 创建菜单
    const menu = document.createElement('div');
    menu.className = 'floor-context-menu';
    menu.style.left = `${e.clientX}px`;
    menu.style.top = `${e.clientY}px`;
    
    // 添加菜单项
    const editOption = document.createElement('div');
    editOption.className = 'menu-option';
    editOption.textContent = '编辑';
    editOption.addEventListener('click', () => {
        showFloorEditModal(floor);
        menu.remove();
    });
    
    const deleteOption = document.createElement('div');
    deleteOption.className = 'menu-option';
    deleteOption.textContent = '删除';
    deleteOption.addEventListener('click', async () => {
        if (confirm(`确定要删除楼层 ${floor.name} 吗？`)) {
            try {
                // 从IndexedDB中删除楼层
                if (mapController.storage && mapController.storage.deleteFloorPlan) {
                    await mapController.storage.deleteFloorPlan(floor.id);
                }
                
                // 从内存中删除楼层
                CONFIG.MOCK.FLOORS = CONFIG.MOCK.FLOORS.filter(f => f.id !== floor.id);
                
                // 重新初始化楼层选择
                initFloorSelection();
                
                // 显示状态消息
                showStatusMessage(`已删除楼层 ${floor.name}`);
                
                // 触发自定义事件，通知其他组件更新
                document.dispatchEvent(new CustomEvent('floorsUpdated'));
            } catch (error) {
                console.error('删除楼层失败:', error);
                showStatusMessage(`删除失败: ${error.message}`, 5000);
            }
        }
        menu.remove();
    });
    
    menu.appendChild(editOption);
    menu.appendChild(deleteOption);
    document.body.appendChild(menu);
    
    // 点击其他地方关闭菜单
    const closeMenu = (e) => {
        if (!menu.contains(e.target)) {
            menu.remove();
            document.removeEventListener('click', closeMenu);
        }
    };
    document.addEventListener('click', closeMenu);
}

/**
 * 显示楼层编辑弹窗
 */
function showFloorEditModal(floor) {
    console.log('showFloorEditModal called with:', floor);
    
    console.log('Initializing modal overlay...');
    let overlay = document.querySelector('.modal-overlay');
    if (!overlay) {
        console.log('Creating new modal overlay...');
        overlay = document.createElement('div');
        overlay.className = 'modal-overlay';
        overlay.id = 'modalOverlay';
        overlay.style.cssText = `
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        `;
        document.body.appendChild(overlay);
        console.log('Modal overlay created:', overlay);
    }

    console.log('Creating modal content...');
    
    // 完全移除现有modal
    document.querySelectorAll('.modal-overlay, .floor-edit-modal').forEach(el => el.remove());
    
    // 重用已存在的overlay变量
    overlay = document.createElement('div');
    overlay.id = 'modalOverlay';
    overlay.style.cssText = `
        display: flex !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background-color: rgba(120,120,120,0.2) !important;
        z-index: 1000 !important;
        justify-content: center !important;
        align-items: center !important;
    `;
    console.log('Overlay style enforced:', overlay.style.cssText);
    
    // 创建modal内容
    const modal = document.createElement('div');
    modal.className = 'floor-edit-modal';
    modal.style.cssText = `
        background: #fff !important;
        padding: 20px !important;
        border-radius: 8px !important;
        min-width: 400px !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2) !important;
        z-index: 1001 !important;
        position: relative !important;
        border: 1px solid #ccc !important;
    `;
    // 构建modal内容
    const modalHTML = `
        <div class="modal-header">
            <h2 class="modal-title">${floor ? '编辑楼层' : '添加楼层'}</h2>
        </div>
        <div class="modal-body">
            <form id="floorEditForm">
                <div class="form-group">
                    <label class="form-label">楼层名称</label>
                    <input type="text" class="form-input" id="floorName" 
                           value="${floor ? floor.name : ''}" required>
                </div>
                <div class="form-group">
                    <label class="form-label">楼层描述</label>
                    <textarea class="form-input" id="floorDescription" 
                           rows="3">${floor ? floor.description || '' : ''}</textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">楼层平面图</label>
                    <input type="file" class="form-input" id="floorPlan" accept="image/*">
                    ${floor && floor.planUrl ? `<div class="current-plan">当前平面图: ${floor.planUrl}</div>` : ''}
                </div>
            </form>
        </div>
        <div class="modal-actions">
            <button class="modal-btn btn-cancel" id="floorEditCancel">取消</button>
            <button class="modal-btn btn-confirm" id="floorEditConfirm">确认</button>
        </div>
    `;
    modal.innerHTML = modalHTML;
    
    // 强制添加到body最上层
    document.body.insertAdjacentElement('afterbegin', overlay);
    overlay.insertAdjacentElement('afterbegin', modal);
    
    console.log('Modal fully initialized:', {
        overlay: overlay,
        modal: modal,
        overlayStyle: window.getComputedStyle(overlay),
        modalStyle: window.getComputedStyle(modal)
    });
    
    // 调试父元素可见性
    console.log('Parent visibility check:');
    console.log('document.body.style.overflow:', document.body.style.overflow);
    console.log('document.documentElement.style.overflow:', document.documentElement.style.overflow);
    console.log('overlay parent visibility:', window.getComputedStyle(overlay.parentElement).visibility);
    
    // 调试z-index层级
    console.log('Z-index hierarchy:');
    console.log('overlay z-index:', window.getComputedStyle(overlay).zIndex);
    console.log('modal z-index:', window.getComputedStyle(modal).zIndex);
    
    console.log('Debug styles applied');

    // 绑定确认按钮事件
    const confirmBtn = modal.querySelector('#floorEditConfirm');
    confirmBtn.addEventListener('click', () => {
        const name = modal.querySelector('#floorName').value.trim();
        const description = modal.querySelector('#floorDescription').value.trim();
        const planFile = modal.querySelector('#floorPlan').files[0];
        
        if (!name) {
            alert('请输入楼层名称');
            return;
        }
        
            // 处理文件上传路径
            const planUrl = planFile ? URL.createObjectURL(planFile) : null;
        
        const saveFloor = async () => {
            if (floor) {
                // 更新现有楼层
                floor.name = name;
                floor.description = description;
                if (planUrl) {
                    floor.planUrl = planUrl;
                }
                
                // 保存到IndexedDB
                if (mapController.storage) {
                    await mapController.storage.saveFloorPlan(floor);
                }
                
                showStatusMessage(`已更新楼层 ${name}`);
            } else {
                // 添加新楼层
                const newFloor = {
                    id: `floor-${Date.now()}`,
                    name,
                    description,
                    planUrl,
                    timestamp: Date.now()
                };
                CONFIG.MOCK.FLOORS.push(newFloor);
                
                // 保存到IndexedDB
                if (mapController.storage) {
                    await mapController.storage.saveFloorPlan(newFloor);
                }
                
                showStatusMessage(`已添加楼层 ${name}`);
            }
            
            initFloorSelection();
            overlay.style.display = 'none';
            
            // 触发自定义事件，通知其他组件更新
            document.dispatchEvent(new CustomEvent('floorsUpdated'));
        };
        
        // 执行保存
        saveFloor().catch(error => {
            console.error('保存楼层数据失败:', error);
            showStatusMessage(`保存失败: ${error.message}`, 5000);
        });
    });

    // 绑定取消按钮事件
    const cancelBtn = modal.querySelector('#floorEditCancel');
    cancelBtn.addEventListener('click', () => {
        overlay.style.display = 'none';
    });
}

/**
 * 初始化浮动按钮
 */
function initFloatingButtons() {
    // 刷新按钮
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
            // 获取当前选中的楼层
            const activeFloor = document.querySelector('.floor-item.active');
            let deviceCount = 150; // 默认设备数量
            
            if (activeFloor) {
                const floorId = activeFloor.dataset.id;
                const floor = CONFIG.MOCK.FLOORS.find(f => f.id === floorId);
                if (floor) {
                    deviceCount = floor.deviceCount;
                }
            }
            
            // 重新生成设备点
            deviceManager.generateDevices(150); // 使用固定设备数量
            
            // 显示状态消息
            showStatusMessage('已刷新设备状态');
        });
    }
    
    // 管理员按钮
    const adminBtn = document.getElementById('adminBtn');
    if (adminBtn && authManager) {
        adminBtn.addEventListener('click', () => {
            authManager.openAdminWindow();
        });
    }
}

// 当DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', initApp);
