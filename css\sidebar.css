/* sidebar.css - 侧边栏样式 */

/* 侧边栏容器 */
.sidebar {
    width: 220px;
    background: var(--bg-primary);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    position: relative;
    padding-bottom: 140px; /* 为底部统计区域留出空间 */
}

.sidebar-section {
    padding: 20px;
}

.floor-title-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-right: 8px;
}

/* 楼层标题行 */
.floor-title-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

/* 楼层列表 */
.floor-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 6px;
}

.floor-item {
    padding: 6px 8px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    cursor: pointer;
    text-align: center;
    transition: var(--transition-fast);
    background: var(--bg-primary);
    font-size: 15px;
    font-weight: bold;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 添加楼层按钮 */
.floor-item.add-floor-btn {
    font-size: 14px;
    width: 24px;
    height: 24px;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    background: rgba(59, 130, 246, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
}

.floor-item.add-floor-btn:hover {
    background: rgba(59, 130, 246, 0.2);
}

/* 右键菜单 */
.floor-context-menu {
    position: fixed;
    background: white;
    border-radius: var(--radius-sm);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 120px;
    overflow: hidden;
}

.menu-option {
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-primary);
}

.menu-option:hover {
    background: var(--bg-secondary);
}

.menu-option:active {
    background: var(--bg-tertiary);
}

/* 编辑弹窗中的表单元素 */
.modal-body .form-group {
    margin-bottom: 16px;
}

.modal-body .form-label {
    display: block;
    margin-bottom: 6px;
    font-size: 14px;
    color: var(--text-primary);
}

.modal-body .form-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-size: 14px;
}

.modal-body .form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.floor-item:hover {
    border-color: var(--primary-color);
    background: #eff6ff;
}

.floor-item.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 实时统计调整 */
.stats-section {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    padding: 0 20px;
}

.stats-title { /* Renamed from .statistics-title */
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.stats-grid { /* Renamed from .statistics-grid */
    display: grid;
    grid-template-columns: 1fr;
    gap: 6px;
}

.stat-card {
    background: var(--bg-primary);
    padding: 8px 12px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
    text-align: center;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 13px;
    line-height: 1.2;
    /* Removed box-shadow, flex-direction, and hover from original modular CSS */
}

.stat-number { /* Renamed from .stat-value */
    font-size: 22px;
    font-weight: normal;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 10px;
    color: var(--text-secondary);
}

.stat-total .stat-number {
    color: var(--primary-color);
}

.stat-normal .stat-number {
    color: var(--success-color);
}

.stat-warning .stat-number {
    color: var(--warning-color);
}

.stat-error .stat-number {
    color: var(--danger-color);
}