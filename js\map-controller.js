/**
 * map-controller.js - 地图控制器模块
 * 负责地图的缩放、平移等交互功能
 */

import CONFIG from './config.js';
import { showStatusMessage } from './ui-utils.js';
import StorageManager from './storage-manager.js';

class MapController {
    constructor() {
        // 初始化存储
        this.storage = new StorageManager();
        
        // 元素引用
        this.mapContainer = document.getElementById('mapContainer');
        this.mapView = document.getElementById('mapViewport'); // 更新为实际ID
        this.zoomInBtn = document.getElementById('zoomIn');
        this.zoomOutBtn = document.getElementById('zoomOut');
        this.zoomResetBtn = document.getElementById('resetView');
        this.zoomLevel = document.getElementById('zoomDisplay');

        // 添加null检查
        if (!this.mapContainer || !this.mapView) {
            console.error('地图容器元素未找到');
            return;
        }
        
        // 状态变量
        this.zoom = CONFIG.MAP.DEFAULT_ZOOM;
        this.panX = 0;
        this.panY = 0;
        this.isDragging = false;
        this.startX = 0;
        this.startY = 0;
        this.lastX = 0;
        this.lastY = 0;
        
        // 绑定方法到实例
        this.handleMouseDown = this.handleMouseDown.bind(this);
        this.handleMouseMove = this.handleMouseMove.bind(this);
        this.handleMouseUp = this.handleMouseUp.bind(this);
        this.handleWheel = this.handleWheel.bind(this);
        this.zoomIn = this.zoomIn.bind(this);
        this.zoomOut = this.zoomOut.bind(this);
        this.resetView = this.resetView.bind(this);
        this.updateTransform = this.updateTransform.bind(this);
        this.updateZoomDisplay = this.updateZoomDisplay.bind(this);
        
        // 初始化事件监听
        // 仅在元素存在时添加事件监听
        if (this.zoomInBtn) this.zoomInBtn.addEventListener('click', this.zoomIn);
        if (this.zoomOutBtn) this.zoomOutBtn.addEventListener('click', this.zoomOut);
        if (this.zoomResetBtn) this.zoomResetBtn.addEventListener('click', this.resetView);
        
        // 确保鼠标和滚轮事件监听器已绑定
        this.initEventListeners();
        
        // 初始化显示
        this.updateTransform();
        this.updateZoomDisplay();
    }
    
    /**
     * 初始化事件监听
     */
    initEventListeners() {
        // 鼠标事件
        this.mapContainer.addEventListener('mousedown', this.handleMouseDown);
        document.addEventListener('mousemove', this.handleMouseMove);
        document.addEventListener('mouseup', this.handleMouseUp);
        
        // 触摸事件
        this.mapContainer.addEventListener('touchstart', (e) => {
            if (e.touches.length === 1) {
                e.preventDefault();
                const touch = e.touches[0];
                this.handleMouseDown({
                    clientX: touch.clientX,
                    clientY: touch.clientY,
                    preventDefault: () => {}
                });
            }
        }, { passive: false });
        
        document.addEventListener('touchmove', (e) => {
            if (e.touches.length === 1 && this.isDragging) {
                e.preventDefault();
                const touch = e.touches[0];
                this.handleMouseMove({
                    clientX: touch.clientX,
                    clientY: touch.clientY,
                    preventDefault: () => {}
                });
            }
        }, { passive: false });
        
        document.addEventListener('touchend', (e) => {
            this.handleMouseUp(e);
        });
        
        // 滚轮缩放
        this.mapContainer.addEventListener('wheel', this.handleWheel, { passive: false });
        
        // 缩放按钮
        this.zoomInBtn.addEventListener('click', this.zoomIn);
        this.zoomOutBtn.addEventListener('click', this.zoomOut);
        this.zoomResetBtn.addEventListener('click', this.resetView);
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + '+' 放大
            if ((e.ctrlKey || e.metaKey) && e.key === '+') {
                e.preventDefault();
                this.zoomIn();
            }
            // Ctrl/Cmd + '-' 缩小
            else if ((e.ctrlKey || e.metaKey) && e.key === '-') {
                e.preventDefault();
                this.zoomOut();
            }
            // Ctrl/Cmd + '0' 重置
            else if ((e.ctrlKey || e.metaKey) && e.key === '0') {
                e.preventDefault();
                this.resetView();
            }
        });
    }
    
    /**
     * 处理鼠标按下事件
     * @param {MouseEvent} e - 鼠标事件
     */
    handleMouseDown(e) {
        e.preventDefault();
        this.isDragging = true;
        this.startX = e.clientX;
        this.startY = e.clientY;
        this.lastX = this.panX;
        this.lastY = this.panY;
        this.mapContainer.classList.add('grabbing');
    }
    
    /**
     * 处理鼠标移动事件
     * @param {MouseEvent} e - 鼠标事件
     */
    handleMouseMove(e) {
        if (!this.isDragging) return;
        
        e.preventDefault();
        const dx = e.clientX - this.startX;
        const dy = e.clientY - this.startY;
        
        this.panX = this.lastX + dx;
        this.panY = this.lastY + dy;
        
        // 使用 requestAnimationFrame 优化性能
        requestAnimationFrame(this.updateTransform);
    }
    
    /**
     * 处理鼠标释放事件
     */
    handleMouseUp() {
        this.isDragging = false;
        this.mapContainer.classList.remove('grabbing');
    }
    
    /**
     * 处理滚轮事件
     * @param {WheelEvent} e - 滚轮事件
     */
    handleWheel(e) {
        e.preventDefault();
        
        // 获取鼠标在视图中的位置
        const rect = this.mapContainer.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        
        // 确定缩放方向
        const delta = -Math.sign(e.deltaY);
        
        if (delta > 0) {
            // 放大
            this.zoomAtPoint(mouseX, mouseY, CONFIG.MAP.ZOOM_STEP);
        } else {
            // 缩小
            this.zoomAtPoint(mouseX, mouseY, -CONFIG.MAP.ZOOM_STEP);
        }
    }
    
    /**
     * 在指定点缩放
     * @param {number} x - 缩放中心点X坐标
     * @param {number} y - 缩放中心点Y坐标
     * @param {number} delta - 缩放增量
     */
    zoomAtPoint(x, y, delta) {
        // 计算缩放前的鼠标位置（相对于地图原点）
        const beforeZoomX = (x - this.panX) / this.zoom;
        const beforeZoomY = (y - this.panY) / this.zoom;
        
        // 应用缩放，限制在100%-600%范围内
        const newZoom = Math.max(
            1.0, // 最小100%
            Math.min(6.0, this.zoom + delta) // 最大600%
        );
        
        // 如果缩放没有变化，则不进行操作
        if (newZoom === this.zoom) return;
        
        // 更新缩放值
        this.zoom = newZoom;
        
        // 当缩放到100%时，重置到中心位置
        if (this.zoom === 1.0) {
            this.panX = 0;
            this.panY = 0;
            this.mapView.classList.add('smooth-transition');
            setTimeout(() => {
                this.mapView.classList.remove('smooth-transition');
            }, 300);
        } else {
            // 计算缩放后的鼠标位置，并调整平移以保持鼠标下的点不变
            this.panX = x - beforeZoomX * this.zoom;
            this.panY = y - beforeZoomY * this.zoom;
        }
        
        // 更新变换和显示
        this.updateTransform();
        this.updateZoomDisplay();
        
        // 显示状态消息
        showStatusMessage(`缩放: ${Math.round(this.zoom * 100)}%`);
    }
    
    /**
     * 在视图中心缩放
     * @param {number} delta - 缩放增量
     */
    zoomAtCenter(delta) {
        const rect = this.mapContainer.getBoundingClientRect();
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        
        this.zoomAtPoint(centerX, centerY, delta);
    }
    
    /**
     * 放大
     */
    zoomIn() {
        this.zoomAtCenter(CONFIG.MAP.ZOOM_STEP);
    }
    
    /**
     * 缩小
     */
    zoomOut() {
        this.zoomAtCenter(-CONFIG.MAP.ZOOM_STEP);
    }
    
    /**
     * 重置视图
     */
    resetView() {
        this.zoom = CONFIG.MAP.DEFAULT_ZOOM;
        this.panX = 0;
        this.panY = 0;
        
        // 平滑过渡到默认位置
        this.mapView.classList.add('smooth-transition');
        this.updateTransform();
        this.updateZoomDisplay();
        
        // 过渡结束后移除平滑效果
        setTimeout(() => {
            this.mapView.classList.remove('smooth-transition');
        }, 300);
        
        showStatusMessage('视图已重置');
    }
    
    /**
     * 更新变换
     */
    updateTransform() {
        this.mapView.style.transform = `translate3d(${this.panX}px, ${this.panY}px, 0) scale(${this.zoom})`;
    }

    /**
     * 显示楼层平面图
     * @param {string} planUrl 平面图URL
     */
    async showFloorPlan(planUrl, isNewUpload = false) {
        // 清除现有平面图
        if (this.currentFloorPlan && this.currentFloorPlan.parentNode) {
            this.mapView.removeChild(this.currentFloorPlan);
        }
        this.currentFloorPlan = null;

        if (!planUrl) return;

        try {
            // 创建图片元素
            this.currentFloorPlan = document.createElement('img');
            this.currentFloorPlan.style.position = 'absolute';
            this.currentFloorPlan.style.top = '0';
            this.currentFloorPlan.style.left = '0';
            this.currentFloorPlan.style.width = '100%';
            this.currentFloorPlan.style.height = '100%';
            this.currentFloorPlan.style.objectFit = 'contain';
            this.currentFloorPlan.style.zIndex = '1';
            
            // 错误处理
            this.currentFloorPlan.onerror = () => {
                console.error('平面图加载失败:', planUrl);
                if (this.currentFloorPlan && this.currentFloorPlan.parentNode) {
                    this.mapView.removeChild(this.currentFloorPlan);
                }
                this.currentFloorPlan = null;
                showStatusMessage('平面图加载失败');
            };
            
            // 加载成功处理
            // 处理图片源设置
            if (typeof planUrl === 'object' && planUrl.base64) {
                // 使用Base64数据
                this.currentFloorPlan.src = `data:image/png;base64,${planUrl.base64}`;
            } else if (typeof planUrl === 'string') {
                // 普通URL或需要转换的URL
                this.loadStoredFloorPlans().then(plans => {
                    const plan = plans.find(p => p.imageUrl === planUrl);
                    if (plan && plan.base64) {
                        this.currentFloorPlan.src = `data:image/png;base64,${plan.base64}`;
                    } else {
                        // 直接加载URL
                        this.currentFloorPlan.src = planUrl;
                    }
                });
            }

            this.currentFloorPlan.onload = async () => {
                this.mapView.appendChild(this.currentFloorPlan);
                showStatusMessage('平面图已加载');
                
                if (isNewUpload) {
                    try {
                        // 创建备份
                        await this.storage.createBackup();
                        
                        // 转换为Base64存储
                        const response = await fetch(planUrl);
                        const blob = await response.blob();
                        const base64 = await this.blobToBase64(blob);
                        
                        const floorData = {
                            id: 'floor-' + Date.now(),
                            imageUrl: planUrl,
                            timestamp: new Date().toISOString(),
                            base64: base64
                        };
                        
                        const saveResult = await this.storage.saveFloorPlan(floorData);
                        if (saveResult) {
                            showStatusMessage('平面图已保存');
                            // 保存成功后更新备份
                            await this.storage.createBackup();
                        } else {
                            showStatusMessage('平面图保存失败');
                        }
                    } catch (error) {
                        console.error('保存平面图失败:', error);
                    }
                }
            };
            
        } catch (error) {
            console.error('平面图初始化错误:', error);
            showStatusMessage('平面图初始化失败');
        }
    }

    async loadStoredFloorPlans() {
        try {
            let floors = await this.storage.getFloorPlans();
            
            // 数据迁移：将Blob数据转换为Base64
            const needsMigration = floors.some(floor => floor.blob && !floor.base64);
            if (needsMigration) {
                const migratedFloors = await Promise.all(floors.map(async floor => {
                    if (floor.blob && !floor.base64) {
                        try {
                            const base64 = await this.blobToBase64(floor.blob);
                            const migratedFloor = {...floor, base64};
                            delete migratedFloor.blob;
                            await this.storage.saveFloorPlan(migratedFloor);
                            return migratedFloor;
                        } catch (e) {
                            console.error('数据迁移失败:', e);
                            return floor;
                        }
                    }
                    return floor;
                }));
                
                floors = migratedFloors.filter(floor => !floor.blob);
            }
            
            return floors;
        } catch (error) {
            console.error('加载存储楼层失败:', error);
            return [];
        }
    }
    
    /**
     * 更新缩放显示
     */
    updateZoomDisplay() {
        this.zoomLevel.textContent = `${Math.round(this.zoom * 100)}%`;
    }

    /**
     * 将Blob对象转换为Base64字符串
     * @param {Blob} blob - 要转换的Blob对象
     * @returns {Promise<string>} Base64字符串
     */
    blobToBase64(blob) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const dataUrl = reader.result;
                resolve(dataUrl.split(',')[1]);
            };
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });
    }
}

export default MapController;