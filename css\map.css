/* map.css - 地图区域样式 */

/* 地图容器 */
.map-container {
    flex: 1;
    min-width: 100%;
    min-height: 100%;
    background: #f1f5f9;
    position: relative;
    overflow: hidden;
    cursor: default; /* 默认指针，由JavaScript动态控制 */
    user-select: none;
    -webkit-user-select: none;
    touch-action: none;
}

.map-container.grabbing {
    cursor: grabbing !important;
}

/* 可拖拽状态的指针样式 */
.map-container.draggable {
    cursor: grab;
}

.map-container.draggable:active {
    cursor: grabbing;
}

/* 地图视图 (Renamed from .map-view) */
.map-viewport {
    width: 100%;
    height: 100%;
    position: relative; /* from zhuye.html (was absolute) */
    transform-origin: 0 0;
    will-change: transform;
}

.map-viewport.smooth-transition {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* from zhuye.html */
}

/* Floor plan styles from zhuye.html */
.floor-plan {
    width: 100%;
    height: 100%;
    min-height: 600px;
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    position: relative;
    border-radius: var(--radius-md);
    margin: 20px;
    box-shadow: var(--shadow-md);
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* 监控点样式 */
.monitor-point {
    position: absolute;
    cursor: pointer;
    pointer-events: auto; /* from zhuye.html */
    z-index: 10;
    transition: transform 0.2s ease;
    will-change: transform; /* from zhuye.html */
    transform: translateZ(0); /* from zhuye.html */
    /* width, height, margin are removed; size comes from SVG */
}

.monitor-point:hover {
    transform: scale(1.3) translateZ(0); /* from zhuye.html */
    z-index: 50; /* from zhuye.html */
}

.monitor-point.highlight {
    animation: pulse-zhuye 2s infinite; /* Renamed to avoid conflict if original pulse is needed elsewhere */
    z-index: 40;
}

.monitor-point svg {
    width: 16px; /* from zhuye.html */
    height: 16px; /* from zhuye.html */
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1)); /* from zhuye.html */
    display: block; /* from zhuye.html */
}

/* 设备状态样式 (Kept from modular, assuming still used) */
.status-normal {
    fill: var(--success-color);
}

.status-warning {
    fill: var(--warning-color);
}

.status-error {
    fill: var(--danger-color);
}

.status-offline {
    fill: var(--secondary-color);
}

/* Keyframes from zhuye.html */
@keyframes pulse-zhuye { /* Renamed from pulse to avoid conflict */
    0%, 100% {
        transform: scale(1) translateZ(0);
    }
    50% {
        transform: scale(1.2) translateZ(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}


/* 工具提示 (Updated to match zhuye.html) */
.tooltip {
    position: fixed;
    background: rgba(15, 23, 42, 0.95); /* from zhuye.html */
    color: white;
    padding: 12px 16px;
    border-radius: var(--radius-md); /* from zhuye.html */
    font-size: 12px; /* from zhuye.html */
    pointer-events: none;
    z-index: 1000;
    max-width: 280px; /* from zhuye.html */
    backdrop-filter: blur(8px); /* from zhuye.html */
    box-shadow: var(--shadow-md);
    line-height: 1.4; /* from zhuye.html */
    display: none; /* from zhuye.html */
    opacity: 0;
    transform: translateY(-8px); /* from zhuye.html */
    transition: opacity 0.2s ease, transform 0.2s ease; /* from zhuye.html */
    will-change: opacity, transform;
}

.tooltip.show {
    display: block; /* To make it visible */
    opacity: 1;
    transform: translateY(0);
}

.tooltip-title {
    font-weight: 600;
    margin-bottom: 8px; /* from zhuye.html */
    color: #f1f5f9; /* from zhuye.html */
    font-size: 12px; /* Match base tooltip font-size, zhuye.html implies this */
}

/* zhuye.html structure for tooltip content */
.tooltip-row {
    margin-bottom: 4px;
    display: flex;
    justify-content: space-between;
}

.tooltip-label {
    color: #cbd5e1; /* from zhuye.html */
}

.tooltip-value {
    font-weight: 500; /* Kept from modular, zhuye.html doesn't specify for base value */
    color: white; /* Default value color from zhuye.html context */
}

/* Status-specific tooltip values (Kept from modular, assuming still used) */
.tooltip-value.normal {
    color: var(--success-color);
}

.tooltip-value.warning {
    color: var(--warning-color);
}

.tooltip-value.error {
    color: var(--danger-color);
}

.tooltip-value.offline {
    color: #a1a1aa; /* Kept from modular */
}

/* Legend and Status Indicator styles removed as they are not in the zhuye.html snippet (lines 630-830 for map related content) */
/* Or, if they were intended to be kept, they would need to be verified against the full zhuye.html */

/* 加载状态 (Kept from modular, assuming still used, zhuye.html snippet doesn't cover this) */
/* Ensure @keyframes spin is defined, which it is above */
.map-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 100;
    text-align: center;
}

.map-loading-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid var(--primary-color);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite; /* Uses the @keyframes spin defined above */
    margin-bottom: 16px;
}

.map-loading-text {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
}