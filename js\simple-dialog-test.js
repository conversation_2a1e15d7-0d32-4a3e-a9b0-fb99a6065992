/**
 * 简化的对话框测试
 * 用于快速验证对话框显示问题
 */

// 创建最简单的测试对话框
function createSimpleDialog() {
    console.log('=== 创建简单测试对话框 ===');

    // 移除现有的测试对话框
    const existing = document.querySelectorAll('.simple-test-dialog');
    existing.forEach(el => el.remove());

    const overlay = document.createElement('div');
    overlay.className = 'simple-test-dialog';
    overlay.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: rgba(255, 0, 0, 0.8) !important;
        z-index: 999999 !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        font-family: Arial, sans-serif !important;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
        background: white !important;
        padding: 30px !important;
        border-radius: 10px !important;
        text-align: center !important;
        border: 5px solid #000 !important;
        max-width: 400px !important;
    `;

    content.innerHTML = `
        <h2 style="color: red; margin: 0 0 20px 0;">🔥 简单测试对话框</h2>
        <p style="margin: 0 0 20px 0;">如果你能看到这个对话框，说明DOM操作正常</p>
        <button onclick="this.closest('.simple-test-dialog').remove()" style="
            background: red;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        ">关闭</button>
    `;

    overlay.appendChild(content);

    console.log('准备添加简单对话框到DOM');
    document.body.appendChild(overlay);
    console.log('简单对话框已添加，元素:', overlay);
    console.log('对话框是否可见:', overlay.offsetWidth > 0 && overlay.offsetHeight > 0);
    console.log('对话框位置:', overlay.getBoundingClientRect());

    // 点击背景关闭
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            overlay.remove();
        }
    });

    return overlay;
}

// 测试防火分区对话框的简化版本
function testSimpleFireZoneDialog() {
    console.log('=== 测试简化防火分区对话框 ===');

    const testData = {
        id: 'test-zone',
        name: '测试防火分区',
        shape: 'polygon',
        area: 5000,
        fireRating: 'A',
        maxOccupancy: 100,
        exitCount: 2,
        sprinklerSystem: true,
        smokeDetection: true,
        createTime: Date.now()
    };

    const overlay = document.createElement('div');
    overlay.className = 'simple-fire-zone-dialog';
    overlay.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: rgba(0, 0, 0, 0.7) !important;
        z-index: 999999 !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
    `;

    const modal = document.createElement('div');
    modal.style.cssText = `
        background: white !important;
        padding: 20px !important;
        border-radius: 8px !important;
        min-width: 400px !important;
        border: 3px solid #ef4444 !important;
    `;

    // 使用简化的HTML，避免复杂的模板字符串
    modal.innerHTML = `
        <h2 style="color: #ef4444; margin: 0 0 20px 0;">🔥 防火分区配置</h2>
        <div style="margin-bottom: 16px;">
            <label style="display: block; margin-bottom: 4px;">分区名称</label>
            <input type="text" value="${testData.name}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        <div style="margin-bottom: 16px;">
            <label style="display: block; margin-bottom: 4px;">防火等级</label>
            <select style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="A">A级 (最高)</option>
                <option value="B">B级 (高)</option>
                <option value="C">C级 (中)</option>
                <option value="D">D级 (低)</option>
            </select>
        </div>
        <div style="text-align: right; margin-top: 20px;">
            <button onclick="this.closest('.simple-fire-zone-dialog').remove()" style="
                background: #6b7280; color: white; border: none; padding: 10px 20px;
                border-radius: 6px; cursor: pointer; margin-right: 10px;
            ">取消</button>
            <button onclick="alert('配置完成！'); this.closest('.simple-fire-zone-dialog').remove();" style="
                background: #ef4444; color: white; border: none; padding: 10px 20px;
                border-radius: 6px; cursor: pointer;
            ">确认</button>
        </div>
    `;

    overlay.appendChild(modal);

    // 移除现有的对话框
    const existing = document.querySelectorAll('.simple-fire-zone-dialog');
    existing.forEach(el => el.remove());

    console.log('添加简化防火分区对话框');
    document.body.appendChild(overlay);
    console.log('对话框已添加，是否可见:', overlay.offsetWidth > 0 && overlay.offsetHeight > 0);

    return overlay;
}

// 检查页面状态
function checkPageStatus() {
    console.log('=== 检查页面状态 ===');
    console.log('document.body:', document.body);
    console.log('body children count:', document.body.children.length);
    console.log('现有模态对话框:', document.querySelectorAll('.modal-overlay, [class*="modal"], [class*="dialog"]'));

    // 检查是否有CSS样式影响
    const testDiv = document.createElement('div');
    testDiv.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: lime;
        color: black;
        padding: 10px;
        z-index: 999999;
        border: 2px solid red;
    `;
    testDiv.textContent = '测试元素 - 2秒后消失';
    document.body.appendChild(testDiv);

    setTimeout(() => {
        testDiv.remove();
        console.log('测试元素已移除');
    }, 2000);

    console.log('页面状态检查完成');
}

// 暴露到全局
window.createSimpleDialog = createSimpleDialog;
window.testSimpleFireZoneDialog = testSimpleFireZoneDialog;
window.checkPageStatus = checkPageStatus;

console.log('简化对话框测试工具已加载');
console.log('可用函数:');
console.log('- createSimpleDialog(): 创建最简单的测试对话框');
console.log('- testSimpleFireZoneDialog(): 测试简化的防火分区对话框');
console.log('- checkPageStatus(): 检查页面状态');

// 创建超级强制的对话框
function forceDialog() {
    console.log('=== 创建超级强制对话框 ===');

    // 移除所有现有对话框
    document.querySelectorAll('[class*="modal"], [class*="dialog"]').forEach(el => el.remove());

    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: rgba(255, 0, 0, 0.9) !important;
        z-index: 2147483647 !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        font-family: Arial !important;
    `;

    overlay.innerHTML = `
        <div style="
            background: white !important;
            padding: 40px !important;
            border: 10px solid black !important;
            border-radius: 20px !important;
            text-align: center !important;
            font-size: 20px !important;
            max-width: 500px !important;
        ">
            <h1 style="color: red !important; margin: 0 0 20px 0 !important;">🚨 强制对话框测试</h1>
            <p style="margin: 0 0 20px 0 !important;">如果你能看到这个，说明对话框可以显示</p>
            <button onclick="this.closest('div').parentElement.remove()" style="
                background: red !important;
                color: white !important;
                border: none !important;
                padding: 15px 30px !important;
                font-size: 18px !important;
                border-radius: 10px !important;
                cursor: pointer !important;
            ">关闭</button>
        </div>
    `;

    document.body.appendChild(overlay);

    // 多重保险
    setTimeout(() => {
        overlay.style.display = 'flex';
        overlay.style.visibility = 'visible';
        overlay.style.opacity = '1';
    }, 50);

    setTimeout(() => {
        overlay.style.display = 'flex';
        overlay.style.visibility = 'visible';
        overlay.style.opacity = '1';
    }, 200);

    console.log('超级强制对话框已创建');
    return overlay;
}

window.forceDialog = forceDialog;
