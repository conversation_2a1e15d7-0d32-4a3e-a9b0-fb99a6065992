/**
 * context-menu-manager.js - 右键菜单管理器
 * 处理地图区域的右键菜单功能
 */

import { showStatusMessage } from './ui-utils.js';

class ContextMenuManager {
    constructor(mapController, deviceManager) {
        this.mapController = mapController;
        this.deviceManager = deviceManager;
        this.currentMenu = null;
        this.clickPosition = { x: 0, y: 0 };
        
        this.initEventListeners();
    }

    /**
     * 初始化事件监听
     */
    initEventListeners() {
        // 监听地图容器的右键事件
        const mapContainer = document.getElementById('mapContainer');
        if (mapContainer) {
            mapContainer.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                this.showContextMenu(e);
            });
        }

        // 点击其他地方关闭菜单
        document.addEventListener('click', (e) => {
            if (this.currentMenu && !this.currentMenu.contains(e.target)) {
                this.hideContextMenu();
            }
        });

        // ESC键关闭菜单
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.currentMenu) {
                this.hideContextMenu();
            }
        });
    }

    /**
     * 显示右键菜单
     * @param {MouseEvent} e - 鼠标事件
     */
    showContextMenu(e) {
        // 隐藏现有菜单
        this.hideContextMenu();

        // 记录点击位置（相对于地图容器）
        const mapContainer = document.getElementById('mapContainer');
        const rect = mapContainer.getBoundingClientRect();
        this.clickPosition = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top,
            pageX: e.clientX,
            pageY: e.clientY
        };

        // 创建菜单
        this.currentMenu = this.createContextMenu();
        
        // 设置菜单位置
        this.currentMenu.style.left = `${e.clientX}px`;
        this.currentMenu.style.top = `${e.clientY}px`;
        
        // 添加到页面
        document.body.appendChild(this.currentMenu);

        // 检查菜单是否超出屏幕边界并调整位置
        this.adjustMenuPosition();
    }

    /**
     * 创建右键菜单
     * @returns {HTMLElement} 菜单元素
     */
    createContextMenu() {
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.style.cssText = `
            position: fixed;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            min-width: 180px;
            padding: 8px 0;
            font-size: 14px;
        `;

        // 菜单项配置
        const menuItems = [
            {
                icon: '🔥',
                text: '添加防火分区',
                action: () => this.addFireZone()
            },
            {
                icon: '🏪',
                text: '添加店铺',
                action: () => this.addShop()
            },
            {
                icon: '📹',
                text: '添加点位',
                action: () => this.addDevice()
            },
            { divider: true },
            {
                icon: '📐',
                text: '测量距离',
                action: () => this.measureDistance()
            },
            {
                icon: '📍',
                text: '标记位置',
                action: () => this.markLocation()
            }
        ];

        // 创建菜单项
        menuItems.forEach(item => {
            if (item.divider) {
                const divider = document.createElement('div');
                divider.style.cssText = `
                    height: 1px;
                    background: #e2e8f0;
                    margin: 4px 0;
                `;
                menu.appendChild(divider);
            } else {
                const menuItem = document.createElement('div');
                menuItem.className = 'context-menu-item';
                menuItem.style.cssText = `
                    padding: 8px 16px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    transition: background-color 0.15s ease;
                `;

                menuItem.innerHTML = `
                    <span style="font-size: 16px;">${item.icon}</span>
                    <span>${item.text}</span>
                `;

                // 悬停效果
                menuItem.addEventListener('mouseenter', () => {
                    menuItem.style.backgroundColor = '#f1f5f9';
                });

                menuItem.addEventListener('mouseleave', () => {
                    menuItem.style.backgroundColor = 'transparent';
                });

                // 点击事件
                menuItem.addEventListener('click', () => {
                    item.action();
                    this.hideContextMenu();
                });

                menu.appendChild(menuItem);
            }
        });

        return menu;
    }

    /**
     * 调整菜单位置，防止超出屏幕
     */
    adjustMenuPosition() {
        if (!this.currentMenu) return;

        const menuRect = this.currentMenu.getBoundingClientRect();
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        let left = parseInt(this.currentMenu.style.left);
        let top = parseInt(this.currentMenu.style.top);

        // 检查右边界
        if (left + menuRect.width > windowWidth) {
            left = windowWidth - menuRect.width - 10;
        }

        // 检查下边界
        if (top + menuRect.height > windowHeight) {
            top = windowHeight - menuRect.height - 10;
        }

        // 检查左边界
        if (left < 10) {
            left = 10;
        }

        // 检查上边界
        if (top < 10) {
            top = 10;
        }

        this.currentMenu.style.left = `${left}px`;
        this.currentMenu.style.top = `${top}px`;
    }

    /**
     * 隐藏右键菜单
     */
    hideContextMenu() {
        if (this.currentMenu) {
            this.currentMenu.remove();
            this.currentMenu = null;
        }
    }

    /**
     * 添加防火分区
     */
    addFireZone() {
        showStatusMessage('正在添加防火分区...');
        // 这里将调用防火分区绘制功能
        console.log('添加防火分区，位置:', this.clickPosition);
        
        // 导入并使用防火分区管理器
        import('./fire-zone-manager.js').then(module => {
            const FireZoneManager = module.default;
            const fireZoneManager = new FireZoneManager(this.mapController);
            fireZoneManager.startDrawing(this.clickPosition);
        }).catch(error => {
            console.error('加载防火分区管理器失败:', error);
            showStatusMessage('加载防火分区功能失败', 3000);
        });
    }

    /**
     * 添加店铺
     */
    addShop() {
        showStatusMessage('正在添加店铺...');
        console.log('添加店铺，位置:', this.clickPosition);
        
        // 导入并使用店铺管理器
        import('./shop-manager.js').then(module => {
            const ShopManager = module.default;
            const shopManager = new ShopManager(this.mapController);
            shopManager.startDrawing(this.clickPosition);
        }).catch(error => {
            console.error('加载店铺管理器失败:', error);
            showStatusMessage('加载店铺功能失败', 3000);
        });
    }

    /**
     * 添加设备点位
     */
    addDevice() {
        showStatusMessage('正在添加设备点位...');
        console.log('添加设备点位，位置:', this.clickPosition);
        
        // 导入并使用设备点位管理器
        import('./device-point-manager.js').then(module => {
            const DevicePointManager = module.default;
            const devicePointManager = new DevicePointManager(this.mapController, this.deviceManager);
            devicePointManager.addDevicePoint(this.clickPosition);
        }).catch(error => {
            console.error('加载设备点位管理器失败:', error);
            showStatusMessage('加载设备点位功能失败', 3000);
        });
    }

    /**
     * 测量距离
     */
    measureDistance() {
        showStatusMessage('点击两个点来测量距离');
        console.log('开始测量距离，起点:', this.clickPosition);
        // TODO: 实现距离测量功能
    }

    /**
     * 标记位置
     */
    markLocation() {
        showStatusMessage('已标记位置');
        console.log('标记位置:', this.clickPosition);
        // TODO: 实现位置标记功能
    }

    /**
     * 获取当前点击位置相对于地图的坐标
     * @returns {Object} 包含x, y坐标的对象
     */
    getMapCoordinates() {
        const mapViewport = document.getElementById('mapViewport');
        if (!mapViewport) return this.clickPosition;

        // 考虑地图的缩放和平移
        const zoom = this.mapController.zoom || 1;
        const panX = this.mapController.panX || 0;
        const panY = this.mapController.panY || 0;

        return {
            x: (this.clickPosition.x - panX) / zoom,
            y: (this.clickPosition.y - panY) / zoom,
            originalX: this.clickPosition.x,
            originalY: this.clickPosition.y
        };
    }
}

export default ContextMenuManager;
