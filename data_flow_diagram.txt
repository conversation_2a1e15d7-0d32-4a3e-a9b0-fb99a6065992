智能监控系统数据流程图

┌─────────────────────────────────────────────────────────────────────────────────┐
│                                    数据流程总览                                   │
└─────────────────────────────────────────────────────────────────────────────────┘

┌───────────────┐     ┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│   用户交互    │────>│   数据处理    │────>│   状态更新    │────>│    UI渲染     │
└───────────────┘     └───────────────┘     └───────────────┘     └───────────────┘
       │                     │                    │                     │
       │                     │                    │                     │
       ▼                     ▼                    ▼                     ▼
┌───────────────┐     ┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│  输入事件捕获  │     │ 数据过滤/转换 │     │ 内存状态维护  │     │ DOM元素更新   │
│  事件委托处理  │     │ 数据验证/计算 │     │ localStorage  │     │ 样式类切换    │
│  表单数据收集  │     │ 业务逻辑处理  │     │ 状态一致性    │     │ 元素创建/删除  │
└───────────────┘     └───────────────┘     └───────────────┘     └───────────────┘


┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  用户认证数据流                                   │
└─────────────────────────────────────────────────────────────────────────────────┘

┌───────────┐    ┌───────────┐    ┌───────────┐    ┌───────────┐    ┌───────────┐
│ 登录表单  │───>│表单数据验证│───>│模拟认证请求│───>│ 保存用户  │───>│ 更新UI   │
└───────────┘    └───────────┘    └───────────┘    └───────────┘    └───────────┘
      │               │                │               │                │
      ▼               ▼                ▼               ▼                ▼
┌──────────────────────────────────────────────────────────────────────────────┐
│ 1. 用户输入用户名和密码                                                       │
│ 2. LoginUI.loginForm.addEventListener('submit') 捕获提交事件                  │
│ 3. AuthManager.handleLogin() 获取表单数据                                    │
│ 4. 验证表单数据完整性                                                         │
│ 5. AuthManager.simulateLogin() 模拟认证请求                                  │
│ 6. 认证成功后调用 AuthManager.loginSuccess()                                  │
│ 7. 将用户信息保存到 localStorage                                              │
│ 8. LoginUI.loginSuccess() 更新UI显示                                         │
│ 9. 隐藏登录按钮，显示用户信息                                                  │
└──────────────────────────────────────────────────────────────────────────────┘


┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  楼层管理数据流                                   │
└─────────────────────────────────────────────────────────────────────────────────┘

┌───────────┐    ┌───────────┐    ┌───────────┐    ┌───────────┐    ┌───────────┐
│ 楼层数据  │───>│ 加载/保存 │───>│ 楼层操作  │───>│ 更新列表  │───>│ 地图更新  │
└───────────┘    └───────────┘    └───────────┘    └───────────┘    └───────────┘
      │               │                │               │                │
      ▼               ▼                ▼               ▼                ▼
┌──────────────────────────────────────────────────────────────────────────────┐
│ 1. FloorManager.init() 初始化楼层管理                                         │
│ 2. FloorManager.loadFloors() 从 localStorage 加载楼层数据                     │
│ 3. FloorManager.floors 数组存储楼层数据                                       │
│ 4. 楼层操作函数 (addFloor, updateFloor, deleteFloor)                          │
│ 5. 操作后调用 FloorManager.saveFloors() 保存到 localStorage                   │
│ 6. FloorManager.updateFloorList() 更新楼层列表UI                              │
│ 7. FloorManagerUI.renderFloorList() 渲染楼层列表                              │
│ 8. 用户点击楼层时触发 FloorManagerUI.handleFloorClick()                        │
│ 9. 更新地图显示对应楼层                                                        │
└──────────────────────────────────────────────────────────────────────────────┘


┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  设备管理数据流                                   │
└─────────────────────────────────────────────────────────────────────────────────┘

┌───────────┐    ┌───────────┐    ┌───────────┐    ┌───────────┐    ┌───────────┐
│ 设备数据  │───>│ 加载/保存 │───>│ 设备操作  │───>│ 更新列表  │───>│ 地图更新  │
└───────────┘    └───────────┘    └───────────┘    └───────────┘    └───────────┘
      │               │                │               │                │
      ▼               ▼                ▼               ▼                ▼
┌──────────────────────────────────────────────────────────────────────────────┐
│ 1. DeviceManager.init() 初始化设备管理                                        │
│ 2. DeviceManager.loadDevices() 加载设备数据                                   │
│ 3. DeviceManager.devices 数组存储设备数据                                     │
│ 4. 设备操作函数 (addDevice, editDevice, deleteDevice)                         │
│ 5. 操作后调用 DeviceManager.saveDevices() 保存数据                            │
│ 6. DeviceManager.renderDeviceList() 更新设备列表UI                            │
│ 7. 生成监控点位 generateMonitorPoints()                                       │
│ 8. 创建设备点位元素 createPointElement()                                       │
│ 9. 更新统计信息 updateStatistics()                                            │
└──────────────────────────────────────────────────────────────────────────────┘


┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  地图交互数据流                                   │
└─────────────────────────────────────────────────────────────────────────────────┘

┌───────────┐    ┌───────────┐    ┌───────────┐    ┌───────────┐    ┌───────────┐
│ 用户操作  │───>│ 事件处理  │───>│ 状态计算  │───>│ 变换应用  │───>│ UI更新   │
└───────────┘    └───────────┘    └───────────┘    └───────────┘    └───────────┘
      │               │                │               │                │
      ▼               ▼                ▼               ▼                ▼
┌──────────────────────────────────────────────────────────────────────────────┐
│ 1. MapController.init() 初始化地图控制器                                       │
│ 2. 用户交互事件监听 (mousedown, mousemove, mouseup, wheel)                     │
│ 3. 拖拽处理 (startDrag, drag, endDrag)                                        │
│ 4. 缩放处理 (handleWheel, zoomAtPoint, zoomAtCenter)                          │
│ 5. 计算新的变换参数 (translateX, translateY, scale)                            │
│ 6. 应用变换 updateTransform()                                                 │
│ 7. 更新UI显示 (缩放百分比, 状态指示器)                                          │
│ 8. 重置视图 resetView()                                                       │
└──────────────────────────────────────────────────────────────────────────────┘


┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  搜索筛选数据流                                   │
└─────────────────────────────────────────────────────────────────────────────────┘

┌───────────┐    ┌───────────┐    ┌───────────┐    ┌───────────┐    ┌───────────┐
│ 搜索输入  │───>│ 延迟处理  │───>│ 数据过滤  │───>│ 点位更新  │───>│ 统计更新  │
└───────────┘    └───────────┘    └───────────┘    └───────────┘    └───────────┘
      │               │                │               │                │
      ▼               ▼                ▼               ▼                ▼
┌──────────────────────────────────────────────────────────────────────────────┐
│ 1. 用户在搜索框输入关键词                                                      │
│ 2. 监听 input 事件，使用 setTimeout 延迟处理                                   │
│ 3. 调用 searchPoints(query) 函数                                              │
│ 4. 过滤 monitorPoints 数组，生成 filteredPoints                               │
│ 5. 根据匹配结果更新点位元素的显示状态 (opacity, filter)                         │
│ 6. 匹配的点位添加 highlight 类                                                │
│ 7. 调用 updateStatistics() 更新统计信息                                       │
│ 8. 使用 animateNumber() 平滑更新数字显示                                      │
└──────────────────────────────────────────────────────────────────────────────┘


┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  数据存储结构                                     │
└─────────────────────────────────────────────────────────────────────────────────┘

1. 楼层数据结构 (FloorManager.floors)
```javascript
[
  {
    id: Number,           // 楼层ID
    name: String,         // 楼层名称
    description: String,  // 楼层描述
    plan: String,         // 平面图 (可能是图片路径或Base64)
    createdAt: String     // 创建时间
  },
  // 更多楼层...
]
```

2. 设备数据结构 (DeviceManager.devices)
```javascript
[
  {
    id: Number,           // 设备ID
    type: String,         // 设备类型 (camera, sensor, alarm, detector)
    name: String,         // 设备名称
    location: String,     // 设备位置
    status: String        // 设备状态 (normal, warning, error, offline)
  },
  // 更多设备...
]
```

3. 监控点位数据结构 (monitorPoints)
```javascript
[
  {
    id: Number,           // 点位ID
    name: String,         // 点位名称
    deviceType: String,   // 设备类型
    status: String,       // 状态
    location: String,     // 位置
    x: Number,            // X坐标
    y: Number,            // Y坐标
    lastUpdate: String    // 最后更新时间
  },
  // 更多点位...
]
```

4. 用户数据结构 (localStorage)
```javascript
{
  isLoggedIn: Boolean,    // 是否已登录
  username: String,       // 用户名
  name: String            // 显示名称
}
```