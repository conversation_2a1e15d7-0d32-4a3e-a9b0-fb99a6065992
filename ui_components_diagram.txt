智能监控系统 UI 组件结构图

┌─────────────────────────────────────────────────────────────────────────────────┐
│                                    整体页面结构                                   │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        │
                ┌───────────────────────┼───────────────────────┐
                │                       │                       │
                ▼                       ▼                       ▼
┌───────────────────────┐  ┌───────────────────────┐  ┌───────────────────────┐
│      顶部导航栏       │  │       主体内容        │  │     登录/认证模块     │
└───────────────────────┘  └───────────────────────┘  └───────────────────────┘
                                        │
                ┌───────────────────────┼───────────────────────┐
                │                       │                       │
                ▼                       ▼                       ▼
┌───────────────────────┐  ┌───────────────────────┐  ┌───────────────────────┐
│       左侧面板        │  │       地图容器        │  │       工具栏组        │
└───────────────────────┘  └───────────────────────┘  └───────────────────────┘
        │                           │                          │
        │                           │                  ┌───────┴───────┐
        │                           │                  │               │
        ▼                           ▼                  ▼               ▼
┌───────────────────┐  ┌───────────────────────┐  ┌─────────┐  ┌───────────────┐
│    楼层管理组件   │  │     地图视口组件      │  │ 顶部工具栏│  │ 底部悬浮工具栏 │
└───────────────────┘  └───────────────────────┘  └─────────┘  └───────────────┘
        │                           │                  │               │
  ┌─────┴─────┐             ┌──────┴──────┐     ┌─────┴─────┐   ┌─────┴─────┐
  │           │             │             │     │           │   │           │
  ▼           ▼             ▼             ▼     ▼           ▼   ▼           ▼
┌─────┐   ┌───────┐   ┌─────────┐   ┌─────────┐ ┌─────┐  ┌─────┐ ┌─────┐  ┌─────┐
│楼层列表│   │统计面板│   │ 平面图  │   │设备点位 │ │搜索框│  │筛选按钮│ │功能按钮│  │版本信息│
└─────┘   └───────┘   └─────────┘   └─────────┘ └─────┘  └─────┘ └─────┘  └─────┘


┌─────────────────────────────────────────────────────────────────────────────────┐
│                                    组件交互关系                                   │
└─────────────────────────────────────────────────────────────────────────────────┘

1. 认证流程
   登录按钮 ──> 登录模态框 ──> 认证管理器 ──> 用户信息显示

2. 楼层管理流程
   楼层列表 ──> 楼层选择 ──> 地图更新 ──> 设备点位更新 ──> 统计信息更新

3. 设备交互流程
   设备点位 ──> 悬浮提示 ──> 设备详情

4. 搜索筛选流程
   搜索框/筛选按钮 ──> 设备过滤 ──> 设备点位高亮/隐藏 ──> 统计信息更新

5. 地图控制流程
   缩放按钮/鼠标滚轮 ──> 地图缩放 ──> 缩放比例显示
   鼠标拖拽 ──> 地图平移


┌─────────────────────────────────────────────────────────────────────────────────┐
│                                    模块依赖关系                                   │
└─────────────────────────────────────────────────────────────────────────────────┘

1. UI核心模块 (ui_core.js)
   ├── 初始化基础UI元素
   └── 绑定基础事件

2. 认证管理模块 (auth_manager.js)
   ├── 依赖: login_ui.js
   ├── 处理登录逻辑
   └── 管理用户会话

3. 楼层管理模块 (floor_manager.js)
   ├── 依赖: floor_manager_ui.js
   ├── 管理楼层数据
   └── 提供楼层CRUD操作

4. 设备管理模块 (device_manager.js)
   ├── 依赖: ui_core.js
   ├── 管理设备数据
   └── 提供设备CRUD操作

5. 地图控制模块 (map_controller.js)
   ├── 依赖: map_ui.js
   ├── 处理地图交互
   └── 管理缩放和平移

6. 地图UI模块 (map_ui.js)
   ├── 依赖: map_controller.js
   └── 管理地图显示

7. 登录UI模块 (login_ui.js)
   └── 管理登录界面

8. 楼层管理UI模块 (floor_manager_ui.js)
   ├── 依赖: floor_manager.js
   └── 管理楼层列表显示


┌─────────────────────────────────────────────────────────────────────────────────┐
│                                    数据流向图                                     │
└─────────────────────────────────────────────────────────────────────────────────┘

用户认证数据流:
登录表单 ──> AuthManager.handleLogin() ──> AuthManager.loginSuccess() ──> LoginUI.loginSuccess() ──> 更新UI

楼层数据流:
FloorManager.loadFloors() ──> FloorManager.floors ──> FloorManagerUI.renderFloorList() ──> 更新UI
楼层选择 ──> FloorManagerUI.handleFloorClick() ──> generateMonitorPoints() ──> 更新地图

设备数据流:
DeviceManager.loadDevices() ──> DeviceManager.devices ──> DeviceManager.renderDeviceList() ──> 更新UI
设备状态变化 ──> 更新设备点位颜色 ──> 更新统计信息

搜索筛选数据流:
搜索输入 ──> searchPoints() ──> 过滤monitorPoints ──> 更新设备点位显示 ──> 更新统计信息