# CORS 问题解决方案

## 问题描述

当直接在浏览器中打开 `file://` 协议的HTML文件时，会遇到CORS（跨域资源共享）错误：

```
Access to script at 'file:///path/to/script.js' from origin 'null' has been blocked by CORS policy
```

这是因为现代浏览器的安全策略不允许从文件系统直接加载ES6模块。

## 解决方案

### 方案1：使用本地HTTP服务器（推荐）

#### 使用Python（推荐）
1. 确保系统已安装Python
2. 在项目根目录打开命令行
3. 运行以下命令：
   ```bash
   python -m http.server 8000
   ```
4. 在浏览器中访问：`http://localhost:8000`

#### 使用批处理文件（最简单）
1. 双击项目根目录下的 `启动服务器.bat` 文件
2. 等待服务器启动
3. 在浏览器中访问：`http://localhost:8000`

#### 使用Node.js
1. 确保系统已安装Node.js
2. 在项目根目录运行：
   ```bash
   node server.js
   ```
3. 在浏览器中访问：`http://localhost:8000`

### 方案2：使用其他HTTP服务器

#### 使用Live Server（VS Code扩展）
1. 在VS Code中安装"Live Server"扩展
2. 右键点击`index.html`文件
3. 选择"Open with Live Server"

#### 使用其他工具
- **http-server**: `npm install -g http-server && http-server`
- **serve**: `npm install -g serve && serve`
- **XAMPP/WAMP**: 将项目放在htdocs目录下

### 方案3：修改浏览器设置（不推荐）

#### Chrome
启动时添加参数：
```bash
chrome.exe --allow-file-access-from-files --disable-web-security --user-data-dir="C:/temp/chrome"
```

#### Firefox
在地址栏输入 `about:config`，设置：
```
security.fileuri.strict_origin_policy = false
```

**注意：修改浏览器安全设置有安全风险，不建议在生产环境使用。**

## 推荐的开发流程

1. **开发阶段**：使用本地HTTP服务器
2. **测试阶段**：使用本地HTTP服务器
3. **部署阶段**：部署到真实的Web服务器

## 常见问题

### Q: 为什么会有CORS限制？
A: 这是浏览器的安全机制，防止恶意脚本访问本地文件系统。

### Q: 可以永久关闭CORS检查吗？
A: 不建议，这会降低浏览器安全性。正确的做法是使用HTTP服务器。

### Q: 部署到服务器后还会有CORS问题吗？
A: 通常不会，CORS主要影响本地开发环境。

## 项目文件说明

- `启动服务器.bat`: 一键启动Python HTTP服务器
- `server.js`: Node.js HTTP服务器脚本
- `index.html`: 项目主页面

## 使用建议

1. **开发时**：使用 `启动服务器.bat` 快速启动
2. **调试时**：使用VS Code的Live Server扩展
3. **部署时**：上传到真实的Web服务器

现在你可以正常访问智能监控系统了！
