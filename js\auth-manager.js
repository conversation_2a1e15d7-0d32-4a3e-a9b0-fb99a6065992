/**
 * auth-manager.js - 认证管理模块
 * 负责登录、注销和用户会话管理
 */

import CONFIG from './config.js';

class AuthManager {
    constructor() {
        // 元素引用
        this.loginBtn = document.getElementById('loginBtn');
        this.userInfo = document.getElementById('userInfo');
        this.userAvatar = document.getElementById('userAvatar');
        this.userName = document.getElementById('userName');
        this.logoutBtn = document.getElementById('logoutBtn');
        this.modalOverlay = document.getElementById('modalOverlay');
        this.loginModal = document.getElementById('loginModal');
        this.loginForm = document.getElementById('loginForm');
        this.usernameInput = document.getElementById('username');
        this.passwordInput = document.getElementById('password');
        this.errorMessage = document.getElementById('errorMessage');
        this.successMessage = document.getElementById('successMessage');
        this.loginSubmitBtn = document.getElementById('loginSubmitBtn');
        this.cancelBtn = document.getElementById('cancelBtn');
        
        // 绑定方法到实例
        this.showLoginModal = this.showLoginModal.bind(this);
        this.hideLoginModal = this.hideLoginModal.bind(this);
        this.handleLogin = this.handleLogin.bind(this);
        this.handleLogout = this.handleLogout.bind(this);
        this.checkAuthStatus = this.checkAuthStatus.bind(this);
        this.openAdminWindow = this.openAdminWindow.bind(this);
        
        // 初始化事件监听
        this.initEventListeners();
        
        // 检查认证状态
        this.checkAuthStatus();
    }
    
    /**
     * 初始化事件监听
     */
    initEventListeners() {
        // 登录按钮点击事件
        if (this.loginBtn) {
            this.loginBtn.addEventListener('click', this.showLoginModal);
        }
        
        // 注销按钮点击事件
        if (this.logoutBtn) {
            this.logoutBtn.addEventListener('click', this.handleLogout);
        }
        
        // 登录表单提交事件
        if (this.loginForm) {
            this.loginForm.addEventListener('submit', this.handleLogin);
        }
        
        // 取消按钮点击事件
        if (this.cancelBtn) {
            this.cancelBtn.addEventListener('click', this.hideLoginModal);
        }
        
        // 点击模态框外部关闭
        if (this.modalOverlay) {
            this.modalOverlay.addEventListener('click', (e) => {
                if (e.target === this.modalOverlay) {
                    this.hideLoginModal();
                }
            });
        }
        
        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modalOverlay.classList.contains('show')) {
                this.hideLoginModal();
            }
        });
    }
    
    /**
     * 显示登录模态框
     */
    showLoginModal() {
        this.modalOverlay.classList.add('show');
        this.usernameInput.focus();
        // 重置表单
        this.loginForm.reset();
        this.errorMessage.classList.remove('show');
        this.successMessage.classList.remove('show');
    }
    
    /**
     * 隐藏登录模态框
     */
    hideLoginModal() {
        this.modalOverlay.classList.remove('show');
    }
    
    /**
     * 处理登录表单提交
     * @param {Event} e - 表单提交事件
     */
    handleLogin(e) {
        e.preventDefault();
        
        const username = this.usernameInput.value.trim();
        const password = this.passwordInput.value.trim();
        
        // 表单验证
        if (!username || !password) {
            this.showError('请输入用户名和密码');
            return;
        }
        
        // 显示加载状态
        this.setLoading(true);
        
        // 模拟API请求延迟
        setTimeout(() => {
            // 在模拟用户数据库中查找用户
            const user = CONFIG.MOCK.USERS.find(u => 
                u.username === username && u.password === password
            );
            
            if (user) {
                // 登录成功
                this.loginSuccess(user);
            } else {
                // 登录失败
                this.showError('用户名或密码错误');
                this.setLoading(false);
            }
        }, 1000);
    }
    
    /**
     * 登录成功处理
     * @param {Object} user - 用户信息
     */
    loginSuccess(user) {
        // 创建认证令牌
        const token = {
            username: user.username,
            name: user.name,
            role: user.role,
            expires: Date.now() + CONFIG.AUTH.TOKEN_EXPIRY
        };
        
        // 保存令牌到本地存储
        localStorage.setItem(CONFIG.AUTH.TOKEN_KEY, JSON.stringify(token));
        
        // 显示成功消息
        this.showSuccess('登录成功，欢迎回来！');
        
        // 更新UI
        this.updateUserInfo(user);
        
        // 延迟关闭模态框
        setTimeout(() => {
            this.hideLoginModal();
            this.setLoading(false);
        }, 1500);
    }
    
    /**
     * 处理注销
     */
    handleLogout() {
        // 移除本地存储中的令牌
        localStorage.removeItem(CONFIG.AUTH.TOKEN_KEY);
        
        // 更新UI
        this.loginBtn.style.display = 'block';
        this.userInfo.style.display = 'none';
    }
    
    /**
     * 检查认证状态
     * @returns {boolean} 是否已认证
     */
    checkAuthStatus() {
        const tokenStr = localStorage.getItem(CONFIG.AUTH.TOKEN_KEY);
        
        if (!tokenStr) {
            return false;
        }
        
        try {
            const token = JSON.parse(tokenStr);
            
            // 检查令牌是否过期
            if (token.expires < Date.now()) {
                // 令牌已过期，清除
                localStorage.removeItem(CONFIG.AUTH.TOKEN_KEY);
                return false;
            }
            
            // 更新UI
            this.updateUserInfo({
                username: token.username,
                name: token.name,
                role: token.role
            });
            
            return true;
        } catch (error) {
            console.error('解析认证令牌失败', error);
            localStorage.removeItem(CONFIG.AUTH.TOKEN_KEY);
            return false;
        }
    }
    
    /**
     * 更新用户信息UI
     * @param {Object} user - 用户信息
     */
    updateUserInfo(user) {
        this.loginBtn.style.display = 'none';
        this.userInfo.style.display = 'flex';
        this.userName.textContent = user.name;
        
        // 设置头像文本（用户名首字母）
        this.userAvatar.textContent = user.name.charAt(0).toUpperCase();
    }
    
    /**
     * 打开管理员窗口
     */
    openAdminWindow() {
        const tokenStr = localStorage.getItem(CONFIG.AUTH.TOKEN_KEY);
        
        if (!tokenStr) {
            alert('请先登录');
            return;
        }
        
        try {
            const token = JSON.parse(tokenStr);
            
            if (token.role === 'admin') {
                const adminWindow = window.open(CONFIG.SYSTEM.ADMIN_URL, '_blank');
                
                // 在管理员窗口关闭前检查认证状态
                if (adminWindow) {
                    adminWindow.addEventListener('beforeunload', () => {
                        this.checkAuthStatus();
                    });
                }
            } else {
                alert('您没有管理员权限');
            }
        } catch (error) {
            console.error('解析认证令牌失败', error);
        }
    }
    
    /**
     * 设置加载状态
     * @param {boolean} isLoading - 是否加载中
     */
    setLoading(isLoading) {
        if (isLoading) {
            this.loginSubmitBtn.disabled = true;
            this.loginSubmitBtn.innerHTML = '<span class="loading-spinner-inline"></span> 登录中...';
        } else {
            this.loginSubmitBtn.disabled = false;
            this.loginSubmitBtn.textContent = '登录';
        }
    }
    
    /**
     * 显示错误消息
     * @param {string} message - 错误消息
     */
    showError(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.classList.add('show');
        this.successMessage.classList.remove('show');
    }
    
    /**
     * 显示成功消息
     * @param {string} message - 成功消息
     */
    showSuccess(message) {
        this.successMessage.textContent = message;
        this.successMessage.classList.add('show');
        this.errorMessage.classList.remove('show');
    }
}

export default AuthManager;