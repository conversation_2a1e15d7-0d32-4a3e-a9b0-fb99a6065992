/**
 * storage-manager.js - 数据存储管理模块
 * 使用IndexedDB进行高性能数据存储，支持楼层数据、设备数据和用户设置
 */

class StorageManager {
    constructor(dbName = 'MonitorSystemDB', version = 2) {
        this.db = null;
        this.dbName = dbName;
        this.version = version;
    }

    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onupgradeneeded = (event) => {
                const db = event.target.result;

                // 楼层数据存储
                if (!db.objectStoreNames.contains('floorPlans')) {
                    const floorStore = db.createObjectStore('floorPlans', {
                        keyPath: 'id'
                    });
                    floorStore.createIndex('timestamp', 'timestamp', { unique: false });
                    floorStore.createIndex('name', 'name', { unique: false });
                }

                // 设备数据存储
                if (!db.objectStoreNames.contains('devices')) {
                    const deviceStore = db.createObjectStore('devices', {
                        keyPath: 'id'
                    });
                    deviceStore.createIndex('floorId', 'floorId', { unique: false });
                    deviceStore.createIndex('type', 'type', { unique: false });
                    deviceStore.createIndex('status', 'status', { unique: false });
                }

                // 用户设置存储
                if (!db.objectStoreNames.contains('settings')) {
                    const settingsStore = db.createObjectStore('settings', {
                        keyPath: 'key'
                    });
                }

                // 系统状态存储
                if (!db.objectStoreNames.contains('systemState')) {
                    const stateStore = db.createObjectStore('systemState', {
                        keyPath: 'key'
                    });
                }
            };

            request.onsuccess = (event) => {
                this.db = event.target.result;
                console.log('IndexedDB初始化成功');
                resolve(this.db);
            };

            request.onerror = (event) => {
                console.error('IndexedDB初始化失败:', event.target.error);
                reject(event.target.error);
            };
        });
    }

    async saveFloorPlan(floorData) {
        try {
            if (!this.db) {
                await this.init();
            }

            // 确保不存储Blob对象
            if (floorData.blob) {
                delete floorData.blob;
            }

            // 强制同步等待事务完成
            const saved = await new Promise((resolve, reject) => {
                const transaction = this.db.transaction(['floorPlans'], 'readwrite');
                let completed = false;

                // 设置超时防止卡死
                const timeout = setTimeout(() => {
                    if (!completed) {
                        console.error('存储操作超时');
                        reject(false);
                    }
                }, 5000);

                transaction.oncomplete = () => {
                    completed = true;
                    clearTimeout(timeout);
                    console.log('楼层数据保存成功:', floorData.id);
                    resolve(true);
                };

                transaction.onerror = (event) => {
                    completed = true;
                    clearTimeout(timeout);
                    console.error('事务错误:', event.target.error);
                    reject(false);
                };

                const store = transaction.objectStore('floorPlans');
                const request = store.put(floorData);

                request.onsuccess = () => {
                    console.log('楼层数据已写入:', floorData.id);
                };

                request.onerror = (event) => {
                    completed = true;
                    clearTimeout(timeout);
                    console.error('保存楼层失败:', event.target.error);
                    reject(false);
                };
            });

            // 验证数据是否真正存储
            if (saved) {
                const verified = await this.verifyStorage(floorData.id);
                if (!verified) {
                    console.error('数据验证失败，存储可能未生效');
                    return false;
                }
            }

            return saved;
        } catch (error) {
            console.error('保存楼层数据时发生异常:', error);
            return false;
        }
    }

    async verifyStorage(id) {
        try {
            const storedData = await this.getFloorPlanById(id);
            return !!storedData;
        } catch (error) {
            console.error('验证存储数据时出错:', error);
            return false;
        }
    }

    async getFloorPlans() {
        try {
            if (!this.db) {
                await this.init();
            }

            return new Promise((resolve, reject) => {
                const transaction = this.db.transaction(['floorPlans'], 'readonly');
                const store = transaction.objectStore('floorPlans');
                const request = store.getAll();

                request.onsuccess = (event) => {
                    const data = event.target.result || [];
                    console.log(`成功获取${data.length}条楼层数据`);
                    resolve(data);
                };

                request.onerror = (event) => {
                    console.error('获取楼层列表失败:', event.target.error);
                    // 尝试从localStorage恢复备份
                    const backup = localStorage.getItem(`${this.dbName}_backup`);
                    if (backup) {
                        console.warn('从备份恢复楼层数据');
                        resolve(JSON.parse(backup));
                    } else {
                        reject([]);
                    }
                };
            });
        } catch (error) {
            console.error('获取楼层数据时发生异常:', error);
            return [];
        }
    }

    // 创建数据备份
    async createBackup() {
        try {
            const plans = await this.getFloorPlans();
            localStorage.setItem(`${this.dbName}_backup`, JSON.stringify(plans));
            console.log('已创建楼层数据备份');
            return true;
        } catch (error) {
            console.error('创建备份失败:', error);
            return false;
        }
    }

    async getFloorPlanById(id) {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['floorPlans'], 'readonly');
            const store = transaction.objectStore('floorPlans');
            const request = store.get(id);

            request.onsuccess = (event) => resolve(event.target.result);
            request.onerror = (event) => {
                console.error('获取楼层详情失败:', event.target.error);
                reject(null);
            };
        });
    }

    async deleteFloorPlan(id) {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['floorPlans'], 'readwrite');
            const store = transaction.objectStore('floorPlans');
            const request = store.delete(id);

            request.onsuccess = () => {
                console.log('楼层删除成功:', id);
                resolve(true);
            };
            request.onerror = (event) => {
                console.error('删除楼层失败:', event.target.error);
                reject(false);
            };
        });
    }

    // ==================== 设备数据管理 ====================

    async saveDevice(deviceData) {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['devices'], 'readwrite');
            const store = transaction.objectStore('devices');
            const request = store.put(deviceData);

            request.onsuccess = () => {
                console.log('设备保存成功:', deviceData.id);
                resolve(true);
            };
            request.onerror = (event) => {
                console.error('保存设备失败:', event.target.error);
                reject(false);
            };
        });
    }

    async getDevices(floorId = null) {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['devices'], 'readonly');
            const store = transaction.objectStore('devices');

            let request;
            if (floorId) {
                const index = store.index('floorId');
                request = index.getAll(floorId);
            } else {
                request = store.getAll();
            }

            request.onsuccess = (event) => {
                const devices = event.target.result || [];
                console.log(`获取到${devices.length}个设备数据`);
                resolve(devices);
            };
            request.onerror = (event) => {
                console.error('获取设备数据失败:', event.target.error);
                reject([]);
            };
        });
    }

    async deleteDevice(id) {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['devices'], 'readwrite');
            const store = transaction.objectStore('devices');
            const request = store.delete(id);

            request.onsuccess = () => {
                console.log('设备删除成功:', id);
                resolve(true);
            };
            request.onerror = (event) => {
                console.error('删除设备失败:', event.target.error);
                reject(false);
            };
        });
    }

    // ==================== 用户设置管理 ====================

    async saveSetting(key, value) {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['settings'], 'readwrite');
            const store = transaction.objectStore('settings');
            const request = store.put({ key, value, timestamp: Date.now() });

            request.onsuccess = () => {
                console.log('设置保存成功:', key);
                resolve(true);
            };
            request.onerror = (event) => {
                console.error('保存设置失败:', event.target.error);
                reject(false);
            };
        });
    }

    async getSetting(key, defaultValue = null) {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['settings'], 'readonly');
            const store = transaction.objectStore('settings');
            const request = store.get(key);

            request.onsuccess = (event) => {
                const result = event.target.result;
                resolve(result ? result.value : defaultValue);
            };
            request.onerror = (event) => {
                console.error('获取设置失败:', event.target.error);
                resolve(defaultValue);
            };
        });
    }

    // ==================== 系统状态管理 ====================

    async saveSystemState(key, state) {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['systemState'], 'readwrite');
            const store = transaction.objectStore('systemState');
            const request = store.put({ key, state, timestamp: Date.now() });

            request.onsuccess = () => {
                console.log('系统状态保存成功:', key);
                resolve(true);
            };
            request.onerror = (event) => {
                console.error('保存系统状态失败:', event.target.error);
                reject(false);
            };
        });
    }

    async getSystemState(key, defaultState = null) {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['systemState'], 'readonly');
            const store = transaction.objectStore('systemState');
            const request = store.get(key);

            request.onsuccess = (event) => {
                const result = event.target.result;
                resolve(result ? result.state : defaultState);
            };
            request.onerror = (event) => {
                console.error('获取系统状态失败:', event.target.error);
                resolve(defaultState);
            };
        });
    }

    // ==================== 数据备份和恢复 ====================

    async exportAllData() {
        if (!this.db) await this.init();

        try {
            const [floors, devices, settings, systemState] = await Promise.all([
                this.getFloorPlans(),
                this.getDevices(),
                this.getAllSettings(),
                this.getAllSystemState()
            ]);

            const exportData = {
                version: this.version,
                timestamp: new Date().toISOString(),
                data: {
                    floors,
                    devices,
                    settings,
                    systemState
                }
            };

            return exportData;
        } catch (error) {
            console.error('导出数据失败:', error);
            return null;
        }
    }

    async importAllData(importData) {
        if (!this.db) await this.init();

        try {
            const { floors, devices, settings, systemState } = importData.data;

            // 清空现有数据
            await this.clearAllData();

            // 导入新数据
            const promises = [];

            if (floors) {
                floors.forEach(floor => promises.push(this.saveFloorPlan(floor)));
            }

            if (devices) {
                devices.forEach(device => promises.push(this.saveDevice(device)));
            }

            if (settings) {
                settings.forEach(setting => promises.push(this.saveSetting(setting.key, setting.value)));
            }

            if (systemState) {
                systemState.forEach(state => promises.push(this.saveSystemState(state.key, state.state)));
            }

            await Promise.all(promises);
            console.log('数据导入成功');
            return true;
        } catch (error) {
            console.error('导入数据失败:', error);
            return false;
        }
    }

    async getAllSettings() {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['settings'], 'readonly');
            const store = transaction.objectStore('settings');
            const request = store.getAll();

            request.onsuccess = (event) => resolve(event.target.result || []);
            request.onerror = (event) => {
                console.error('获取所有设置失败:', event.target.error);
                reject([]);
            };
        });
    }

    async getAllSystemState() {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['systemState'], 'readonly');
            const store = transaction.objectStore('systemState');
            const request = store.getAll();

            request.onsuccess = (event) => resolve(event.target.result || []);
            request.onerror = (event) => {
                console.error('获取所有系统状态失败:', event.target.error);
                reject([]);
            };
        });
    }

    async clearAllData() {
        if (!this.db) await this.init();

        const storeNames = ['floorPlans', 'devices', 'settings', 'systemState'];
        const promises = storeNames.map(storeName => {
            return new Promise((resolve, reject) => {
                const transaction = this.db.transaction([storeName], 'readwrite');
                const store = transaction.objectStore(storeName);
                const request = store.clear();

                request.onsuccess = () => resolve();
                request.onerror = (event) => {
                    console.error(`清空${storeName}失败:`, event.target.error);
                    reject(event.target.error);
                };
            });
        });

        await Promise.all(promises);
        console.log('所有数据已清空');
    }
}

export default StorageManager;
