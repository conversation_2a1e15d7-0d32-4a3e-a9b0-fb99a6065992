/**
 * storage-manager.js - 数据存储管理模块
 * 使用IndexedDB进行高性能数据存储
 */

class StorageManager {
    constructor(dbName = 'FloorPlanDB', version = 1) {
        this.db = null;
        this.dbName = dbName;
        this.version = version;
    }

    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains('floorPlans')) {
                    const store = db.createObjectStore('floorPlans', {
                        keyPath: 'id',
                        autoIncrement: true
                    });
                    store.createIndex('timestamp', 'timestamp', { unique: false });
                }
            };
            
            request.onsuccess = (event) => {
                this.db = event.target.result;
                resolve(this.db);
            };
            
            request.onerror = (event) => {
                console.error('IndexedDB初始化失败:', event.target.error);
                reject(event.target.error);
            };
        });
    }

    async saveFloorPlan(floorData) {
        try {
            if (!this.db) {
                await this.init();
            }
            
            // 确保不存储Blob对象
            if (floorData.blob) {
                delete floorData.blob;
            }
            
            // 强制同步等待事务完成
            const saved = await new Promise((resolve, reject) => {
                const transaction = this.db.transaction(['floorPlans'], 'readwrite');
                let completed = false;
                
                // 设置超时防止卡死
                const timeout = setTimeout(() => {
                    if (!completed) {
                        console.error('存储操作超时');
                        reject(false);
                    }
                }, 5000);
                
                transaction.oncomplete = () => {
                    completed = true;
                    clearTimeout(timeout);
                    console.log('楼层数据保存成功:', floorData.id);
                    resolve(true);
                };
                
                transaction.onerror = (event) => {
                    completed = true;
                    clearTimeout(timeout);
                    console.error('事务错误:', event.target.error);
                    reject(false);
                };
                
                const store = transaction.objectStore('floorPlans');
                const request = store.put(floorData);
                
                request.onsuccess = () => {
                    console.log('楼层数据已写入:', floorData.id);
                };
                
                request.onerror = (event) => {
                    completed = true;
                    clearTimeout(timeout);
                    console.error('保存楼层失败:', event.target.error);
                    reject(false);
                };
            });
            
            // 验证数据是否真正存储
            if (saved) {
                const verified = await this.verifyStorage(floorData.id);
                if (!verified) {
                    console.error('数据验证失败，存储可能未生效');
                    return false;
                }
            }
            
            return saved;
        } catch (error) {
            console.error('保存楼层数据时发生异常:', error);
            return false;
        }
    }

    async verifyStorage(id) {
        try {
            const storedData = await this.getFloorPlanById(id);
            return !!storedData;
        } catch (error) {
            console.error('验证存储数据时出错:', error);
            return false;
        }
    }

    async getFloorPlans() {
        try {
            if (!this.db) {
                await this.init();
            }
            
            return new Promise((resolve, reject) => {
                const transaction = this.db.transaction(['floorPlans'], 'readonly');
                const store = transaction.objectStore('floorPlans');
                const request = store.getAll();
                
                request.onsuccess = (event) => {
                    const data = event.target.result || [];
                    console.log(`成功获取${data.length}条楼层数据`);
                    resolve(data);
                };
                
                request.onerror = (event) => {
                    console.error('获取楼层列表失败:', event.target.error);
                    // 尝试从localStorage恢复备份
                    const backup = localStorage.getItem(`${this.dbName}_backup`);
                    if (backup) {
                        console.warn('从备份恢复楼层数据');
                        resolve(JSON.parse(backup));
                    } else {
                        reject([]);
                    }
                };
            });
        } catch (error) {
            console.error('获取楼层数据时发生异常:', error);
            return [];
        }
    }

    // 创建数据备份
    async createBackup() {
        try {
            const plans = await this.getFloorPlans();
            localStorage.setItem(`${this.dbName}_backup`, JSON.stringify(plans));
            console.log('已创建楼层数据备份');
            return true;
        } catch (error) {
            console.error('创建备份失败:', error);
            return false;
        }
    }

    async getFloorPlanById(id) {
        if (!this.db) await this.init();
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['floorPlans'], 'readonly');
            const store = transaction.objectStore('floorPlans');
            const request = store.get(id);
            
            request.onsuccess = (event) => resolve(event.target.result);
            request.onerror = (event) => {
                console.error('获取楼层详情失败:', event.target.error);
                reject(null);
            };
        });
    }
    
    /**
     * 删除指定ID的楼层数据
     * @param {string} id - 楼层ID
     * @returns {Promise<boolean>} 是否删除成功
     */
    async deleteFloorPlan(id) {
        if (!this.db) {
            await this.init();
        }
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['floorPlans'], 'readwrite');
            const store = transaction.objectStore('floorPlans');
            const request = store.delete(id);
            
            request.onsuccess = () => {
                console.log(`成功删除楼层数据: ${id}`);
                resolve(true);
            };
            
            request.onerror = (event) => {
                console.error('删除楼层数据失败:', event.target.error);
                reject(event.target.error);
            };
        });
    }
}

export default StorageManager;
