/**
 * 对话框测试工具
 * 用于测试配置对话框是否能正常显示
 */

/**
 * 测试防火分区配置对话框
 */
function testFireZoneDialog() {
    console.log('=== 测试防火分区配置对话框 ===');
    
    // 创建测试数据
    const testData = {
        id: 'test-fire-zone-' + Date.now(),
        name: '测试防火分区',
        shape: 'polygon',
        points: [
            {x: 100, y: 100},
            {x: 200, y: 100},
            {x: 200, y: 200},
            {x: 100, y: 200}
        ],
        area: 10000,
        fireRating: 'A',
        maxOccupancy: 100,
        exitCount: 2,
        sprinklerSystem: true,
        smokeDetection: true,
        fireAlarm: false,
        fireExtinguisher: false,
        emergencyLighting: false,
        ventilationSystem: false,
        riskLevel: 'medium',
        inspectionCycle: 30,
        evacuationDistance: 30,
        description: '这是一个测试防火分区',
        createTime: Date.now()
    };
    
    // 创建对话框
    const overlay = document.createElement('div');
    overlay.className = 'modal-overlay fire-zone-config-modal test-modal';
    overlay.style.cssText = `
        display: flex !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background-color: rgba(0,0,0,0.7) !important;
        z-index: 99999 !important;
        justify-content: center !important;
        align-items: center !important;
    `;

    const modal = document.createElement('div');
    modal.style.cssText = `
        background: #fff !important;
        padding: 24px !important;
        border-radius: 8px !important;
        min-width: 400px !important;
        max-width: 500px !important;
        max-height: 80vh !important;
        overflow-y: auto !important;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3) !important;
        border: 3px solid #ef4444 !important;
    `;

    modal.innerHTML = `
        <div style="text-align: center;">
            <h2 style="color: #ef4444; margin: 0 0 20px 0;">🔥 测试防火分区配置对话框</h2>
            <p style="margin: 0 0 20px 0; color: #666;">
                如果你能看到这个对话框，说明DOM操作正常工作
            </p>
            <div style="background: #f0f0f0; padding: 16px; border-radius: 4px; margin: 16px 0; text-align: left;">
                <h4 style="margin: 0 0 8px 0;">测试数据:</h4>
                <div style="font-family: monospace; font-size: 12px;">
                    <div>ID: ${testData.id}</div>
                    <div>名称: ${testData.name}</div>
                    <div>形状: ${testData.shape}</div>
                    <div>点位数量: ${testData.points.length}</div>
                    <div>面积: ${testData.area}</div>
                </div>
            </div>
            <button id="closeTestDialog" style="
                background: #ef4444; 
                color: white; 
                border: none; 
                padding: 10px 20px;
                border-radius: 6px; 
                cursor: pointer; 
                font-size: 14px;
                margin: 0 8px;
            ">关闭测试对话框</button>
            <button id="testRealDialog" style="
                background: #10b981; 
                color: white; 
                border: none; 
                padding: 10px 20px;
                border-radius: 6px; 
                cursor: pointer; 
                font-size: 14px;
                margin: 0 8px;
            ">测试真实对话框</button>
        </div>
    `;

    overlay.appendChild(modal);
    
    // 移除现有的测试对话框
    const existingTestModals = document.querySelectorAll('.test-modal');
    existingTestModals.forEach(modal => modal.remove());
    
    console.log('添加测试对话框到DOM');
    document.body.appendChild(overlay);
    
    // 绑定事件
    const closeBtn = overlay.querySelector('#closeTestDialog');
    const testRealBtn = overlay.querySelector('#testRealDialog');
    
    closeBtn.addEventListener('click', () => {
        console.log('关闭测试对话框');
        overlay.remove();
    });
    
    testRealBtn.addEventListener('click', () => {
        console.log('测试真实防火分区对话框');
        overlay.remove();
        
        // 尝试调用真实的防火分区对话框
        if (window.contextMenuManager && window.contextMenuManager.fireZoneManager) {
            window.contextMenuManager.fireZoneManager.showZoneConfigDialog(testData, (data) => {
                console.log('真实对话框测试完成:', data);
            });
        } else {
            console.error('找不到防火分区管理器');
        }
    });
    
    // 点击背景关闭
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            overlay.remove();
        }
    });
    
    console.log('测试对话框已创建');
}

/**
 * 测试店铺配置对话框
 */
function testShopDialog() {
    console.log('=== 测试店铺配置对话框 ===');
    
    const testData = {
        id: 'test-shop-' + Date.now(),
        name: '测试店铺',
        shape: 'polygon',
        points: [
            {x: 150, y: 150},
            {x: 250, y: 150},
            {x: 250, y: 250},
            {x: 150, y: 250}
        ],
        area: 10000,
        category: 'retail',
        owner: '测试店主',
        phone: '***********',
        businessHours: '09:00-21:00',
        rent: 5000,
        status: 'operating',
        description: '这是一个测试店铺',
        createTime: new Date().toISOString()
    };
    
    // 类似的测试对话框创建逻辑...
    console.log('店铺测试数据:', testData);
    alert('店铺对话框测试 - 请查看控制台');
}

/**
 * 检查页面环境
 */
function checkEnvironment() {
    console.log('=== 检查页面环境 ===');
    console.log('document.body:', document.body);
    console.log('现有模态对话框:', document.querySelectorAll('.modal-overlay'));
    console.log('地图容器:', document.getElementById('mapContainer'));
    console.log('地图视口:', document.getElementById('mapViewport'));
    console.log('contextMenuManager:', window.contextMenuManager);
    
    // 检查CSS样式
    const testDiv = document.createElement('div');
    testDiv.style.cssText = 'position: fixed; z-index: 99999; top: 10px; right: 10px; background: red; color: white; padding: 10px;';
    testDiv.textContent = '测试元素';
    document.body.appendChild(testDiv);
    
    setTimeout(() => {
        testDiv.remove();
    }, 2000);
    
    console.log('环境检查完成');
}

// 将函数暴露到全局作用域，方便在控制台调用
window.testFireZoneDialog = testFireZoneDialog;
window.testShopDialog = testShopDialog;
window.checkEnvironment = checkEnvironment;

console.log('对话框测试工具已加载');
console.log('可用函数:');
console.log('- testFireZoneDialog(): 测试防火分区配置对话框');
console.log('- testShopDialog(): 测试店铺配置对话框');
console.log('- checkEnvironment(): 检查页面环境');
