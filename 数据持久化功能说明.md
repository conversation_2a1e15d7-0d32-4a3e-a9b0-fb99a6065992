# 智能监控系统 - 数据持久化功能说明

## 功能概述

现在你的智能监控系统已经具备了完整的数据持久化功能，可以保证用户添加的楼层和其他数据在下次打开页面时仍然存在。

## 🔧 实现的功能

### 1. 数据存储
- **IndexedDB存储**: 使用浏览器的IndexedDB数据库进行高性能数据存储
- **楼层数据**: 保存楼层信息、平面图、描述等
- **设备数据**: 保存设备信息和状态
- **用户设置**: 保存地图缩放、位置、最后选中楼层等
- **系统状态**: 保存统计信息、搜索历史等

### 2. 自动保存机制
- **实时保存**: 用户操作后立即保存到数据库
- **定期保存**: 每30秒自动保存用户设置
- **页面卸载保存**: 关闭页面时自动保存所有数据
- **操作触发保存**: 楼层切换、地图操作等会触发保存

### 3. 数据管理界面
- **数据导出**: 将所有数据导出为JSON文件
- **数据导入**: 从备份文件恢复数据
- **数据备份**: 创建本地备份
- **数据清空**: 清空所有数据（危险操作）

## 📋 使用说明

### 添加楼层数据持久化
1. 点击楼层列表旁的"+"按钮
2. 填写楼层名称、描述
3. 可选择上传楼层平面图
4. 点击"确认"保存
5. **数据会自动保存到IndexedDB数据库**

### 编辑楼层数据持久化
1. 右键点击楼层项
2. 选择"编辑"
3. 修改楼层信息
4. 点击"确认"保存
5. **修改会自动保存到数据库**

### 删除楼层数据持久化
1. 右键点击楼层项
2. 选择"删除"
3. 确认删除操作
4. **数据会从数据库中永久删除**

### 数据管理操作
1. 点击顶部导航栏的"数据管理"按钮
2. 在弹出的界面中可以：
   - **导出数据**: 下载包含所有数据的JSON文件
   - **导入数据**: 从备份文件恢复数据
   - **创建备份**: 在localStorage中创建备份
   - **清空数据**: 删除所有数据（谨慎操作）

## 🗄️ 数据存储结构

### IndexedDB数据库结构
```
MonitorSystemDB (数据库)
├── floorPlans (楼层数据表)
│   ├── id (主键)
│   ├── name (楼层名称)
│   ├── description (楼层描述)
│   ├── planUrl (平面图URL)
│   └── timestamp (时间戳)
├── devices (设备数据表)
│   ├── id (主键)
│   ├── floorId (所属楼层)
│   ├── type (设备类型)
│   └── status (设备状态)
├── settings (用户设置表)
│   ├── key (设置键)
│   ├── value (设置值)
│   └── timestamp (时间戳)
└── systemState (系统状态表)
    ├── key (状态键)
    ├── state (状态值)
    └── timestamp (时间戳)
```

### 保存的数据类型
1. **楼层数据**
   - 楼层ID、名称、描述
   - 楼层平面图（Base64格式）
   - 创建和修改时间

2. **用户设置**
   - 地图缩放级别
   - 地图平移位置
   - 最后选中的楼层

3. **系统状态**
   - 设备统计信息
   - 搜索历史记录

## 🔄 数据恢复机制

### 启动时数据加载
1. 系统启动时自动初始化IndexedDB
2. 加载保存的楼层数据
3. 恢复用户设置（地图状态、选中楼层等）
4. 如果没有保存数据，使用默认配置

### 数据备份策略
1. **自动备份**: 每次保存数据时创建localStorage备份
2. **手动备份**: 用户可以导出JSON文件备份
3. **数据验证**: 导入时验证数据格式和完整性

## ⚠️ 注意事项

### 数据安全
- 数据存储在用户本地浏览器中
- 清除浏览器数据会删除所有保存的信息
- 建议定期导出数据进行备份

### 浏览器兼容性
- 需要支持IndexedDB的现代浏览器
- Chrome、Firefox、Safari、Edge等主流浏览器均支持

### 存储限制
- IndexedDB存储空间受浏览器限制
- 一般情况下有几GB的存储空间
- 大量平面图可能占用较多空间

## 🚀 使用建议

1. **定期备份**: 建议定期使用"导出数据"功能备份重要数据
2. **测试恢复**: 在重要操作前测试数据导入功能
3. **分环境使用**: 开发和生产环境可以使用不同的数据
4. **监控存储**: 注意浏览器存储空间使用情况

## 🔧 技术实现

### 核心模块
- `storage-manager.js`: 数据存储管理
- `data-manager.js`: 数据导入导出
- `main.js`: 数据加载和保存逻辑

### 关键技术
- IndexedDB API
- File API (文件读写)
- Blob API (数据导出)
- Promise/async-await (异步处理)

现在你的智能监控系统具备了完整的数据持久化能力，用户添加的楼层和设置都会被安全保存，下次打开页面时会自动恢复！
