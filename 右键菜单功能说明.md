# 智能监控系统 - 右键菜单功能说明

## 功能概述

现在你的智能监控系统已经具备了完整的右键菜单功能，可以在地图区域右键点击弹出选项菜单，包含"添加防火分区"、"添加店铺"、"添加点位"等功能。

## 🎯 实现的功能

### 1. 右键菜单系统
- **右键触发**: 在地图显示区域右键点击弹出菜单
- **菜单选项**: 添加防火分区、添加店铺、添加点位、测量距离、标记位置
- **智能定位**: 菜单自动调整位置，防止超出屏幕边界
- **多种关闭方式**: 点击其他区域、按ESC键、点击菜单项后自动关闭

### 2. 添加防火分区功能
- **多种形状**: 支持矩形、圆形、多边形防火分区
- **交互绘制**: 点击绘制矩形和圆形，多点绘制多边形
- **参数配置**: 防火等级、最大容量、出口数量、消防设施等
- **可视化显示**: 红色边框和半透明填充，带标签显示

### 3. 添加店铺功能
- **店铺形状**: 支持矩形和多边形店铺区域
- **详细信息**: 店铺名称、类型、店主、联系方式、营业时间等
- **经营状态**: 正常营业、装修中、暂停营业、空置
- **可视化显示**: 绿色边框和半透明填充，带图标和标签

### 4. 添加设备点位功能
- **设备类型**: 摄像头、传感器、报警器、探测器
- **设备状态**: 正常、警告、错误、离线
- **外观设置**: 可调整图标大小和旋转角度
- **网络配置**: IP地址、端口、设备型号等
- **可视化显示**: 圆形图标，颜色表示状态

## 📋 使用说明

### 基本操作流程

1. **触发右键菜单**
   - 在地图显示区域右键点击
   - 菜单会在鼠标位置弹出

2. **选择功能**
   - 点击"添加防火分区"、"添加店铺"或"添加点位"
   - 菜单自动关闭，进入相应的添加模式

3. **绘制或配置**
   - 根据选择的功能进行绘制或配置
   - 填写相关参数和信息

4. **确认保存**
   - 完成配置后点击确认
   - 数据自动保存到数据库

### 详细操作指南

#### 添加防火分区
1. 右键点击地图 → 选择"添加防火分区"
2. 选择形状类型：
   - **矩形区域**: 点击两个对角点
   - **圆形区域**: 点击中心点和边缘点
   - **多边形区域**: 点击多个点，按Enter完成
3. 配置防火分区参数（功能待完善）
4. 确认添加，在地图上显示红色区域

#### 添加店铺
1. 右键点击地图 → 选择"添加店铺"
2. 选择店铺形状：
   - **矩形店铺**: 标准店铺布局
   - **不规则店铺**: 自定义形状
3. 填写店铺信息：
   - 店铺名称、类型、店主姓名
   - 联系电话、营业时间
   - 经营状态、月租金
   - 店铺描述
4. 确认添加，在地图上显示绿色区域

#### 添加设备点位
1. 右键点击地图 → 选择"添加点位"
2. 配置设备信息：
   - 设备名称、类型、状态
   - 安装日期、设备描述
   - 图标大小、旋转角度
   - IP地址、端口、设备型号
3. 确认添加，在地图上显示设备图标

## 🎨 视觉设计

### 防火分区样式
- **边框**: 2px 红色实线 (#ef4444)
- **填充**: 红色半透明 (rgba(239, 68, 68, 0.1))
- **标签**: 红色背景，白色文字
- **图标**: 🔥 火焰图标

### 店铺区域样式
- **边框**: 2px 实线，颜色根据状态变化
  - 正常营业: 绿色 (#10b981)
  - 装修中: 黄色 (#f59e0b)
  - 暂停营业: 红色 (#ef4444)
  - 空置: 灰色 (#6b7280)
- **填充**: 对应颜色的半透明背景
- **标签**: 状态色背景，带类型图标
- **图标**: 🛍️🍽️🔧🎮📚📦 等类型图标

### 设备点位样式
- **形状**: 圆形图标
- **颜色**: 根据设备状态
  - 正常: 绿色 (#10b981)
  - 警告: 黄色 (#f59e0b)
  - 错误: 红色 (#ef4444)
  - 离线: 灰色 (#6b7280)
- **图标**: 📹🌡️🚨🔍 等设备图标
- **大小**: 16-48px 可调节
- **旋转**: 0-360° 可调节

## 🔧 技术实现

### 核心模块
- `context-menu-manager.js`: 右键菜单管理器
- `fire-zone-manager.js`: 防火分区管理器
- `shop-manager.js`: 店铺管理器
- `device-point-manager.js`: 设备点位管理器

### 关键技术
- **事件处理**: 右键菜单事件监听和处理
- **动态绘制**: Canvas/SVG 图形绘制
- **模态对话框**: 配置界面的动态生成
- **数据持久化**: IndexedDB 数据存储
- **坐标转换**: 屏幕坐标到地图坐标的转换

### 数据结构

#### 防火分区数据
```javascript
{
  id: "fire-zone-1234567890",
  name: "防火分区A",
  shape: "rectangle", // rectangle, circle, polygon
  points: [{x: 100, y: 100}, {x: 200, y: 200}],
  area: 10000,
  fireRating: "A",
  maxOccupancy: 100,
  exitCount: 2,
  sprinklerSystem: true,
  smokeDetection: true
}
```

#### 店铺数据
```javascript
{
  id: "shop-1234567890",
  name: "测试店铺",
  shape: "rectangle", // rectangle, polygon
  points: [{x: 100, y: 100}, {x: 200, y: 200}],
  area: 10000,
  category: "retail", // retail, restaurant, service, etc.
  owner: "张三",
  phone: "***********",
  businessHours: "09:00-21:00",
  rent: 5000,
  status: "operating" // operating, renovation, closed, vacant
}
```

#### 设备点位数据
```javascript
{
  id: "device-1234567890",
  name: "摄像头01",
  type: "camera", // camera, sensor, alarm, detector
  status: "normal", // normal, warning, error, offline
  x: 150,
  y: 150,
  size: 24,
  rotation: 0,
  ip: "*************",
  port: 80,
  model: "DS-2CD2T47G1-L"
}
```

## 🚀 扩展功能

### 已实现
- ✅ 右键菜单基础框架
- ✅ 防火分区绘制（基础版）
- ✅ 店铺区域绘制
- ✅ 设备点位添加
- ✅ 数据配置对话框
- ✅ 可视化显示

### 待完善
- 🔄 防火分区详细配置对话框
- 🔄 多边形绘制的实时预览
- 🔄 元素选择和编辑功能
- 🔄 元素删除功能
- 🔄 提示框显示详细信息
- 🔄 测量距离功能
- 🔄 位置标记功能

## 📝 使用建议

1. **绘制顺序**: 建议先绘制防火分区，再添加店铺，最后添加设备点位
2. **命名规范**: 使用有意义的名称，便于后续管理
3. **数据备份**: 定期使用数据管理功能导出备份
4. **性能考虑**: 避免在同一区域添加过多元素

现在你可以在地图上右键点击，体验完整的添加功能了！
