<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能监控系统</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="css/sidebar.css">
    <link rel="stylesheet" href="css/map.css">
    <link rel="stylesheet" href="css/toolbar.css">
</head>
<body>
    <!-- 顶部导航 -->
    <div class="header">
        <h1>智能监控系统</h1>
        <div class="header-buttons">
            <button class="header-btn" id="dataManagementBtn">数据管理</button>
            <button class="header-btn">设置</button>
            <button class="header-btn">帮助</button>
            <button class="header-btn">关于</button>
            <!-- 登录按钮 -->
            <button class="login-btn" id="loginBtn">登录</button>

            <!-- 用户信息区域 - 登录后显示 -->
            <div class="user-info" id="userInfo">
                <div class="user-avatar" id="userAvatar">A</div>
                <span class="user-name" id="userName">管理员</span>
                <button class="logout-btn" id="logoutBtn">退出</button>
            </div>
        </div>
    </div>

    <!-- 登录弹窗 -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="login-modal">
            <div class="modal-header">
                <h2 class="modal-title">系统登录</h2>
                <p class="modal-subtitle">请输入您的账号和密码</p>
            </div>

            <div class="modal-body">
                <!-- 错误提示 -->
                <div class="error-message" id="errorMessage"></div>

                <!-- 成功提示 -->
                <div class="success-message" id="successMessage"></div>

                <form id="loginForm">
                    <div class="form-group">
                        <label class="form-label" for="username">用户名</label>
                        <input type="text" id="username" class="form-input"
                               placeholder="请输入用户名" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="password">密码</label>
                        <input type="password" id="password" class="form-input"
                               placeholder="请输入密码" required>
                    </div>
                </form>
            </div>

            <div class="modal-actions">
                <button class="modal-btn btn-cancel" id="cancelBtn">取消</button>
                <button class="modal-btn btn-confirm" id="confirmBtn">
                    <span id="loginBtnText">登录</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="container">
        <!-- 左侧面板 -->
        <div class="sidebar">
            <div class="sidebar-section">
                <div class="floor-title-row">
                    <div class="section-title">楼层选择</div>
                    <!-- "+"按钮将由main.js动态添加到这里 -->
                </div>
                <div class="floor-list" id="floorList">
                    <div class="floor-item">1楼</div>
                    <div class="floor-item">2楼</div>
                    <div class="floor-item active">3楼</div>
                    <div class="floor-item">4楼</div>
                    <div class="floor-item">5楼</div>
                    <div class="floor-item">6楼</div>
                    <div class="floor-item">7楼</div>
                    <div class="floor-item">8楼</div>
                </div>
            </div>

            <!-- 实时统计 -->
            <div class="stats-section">
                <div class="stats-title">实时统计</div>
                <div class="stats-grid">
                    <div class="stat-card stat-total">
                        <div class="stat-number" id="totalPoints">1000</div>
                        <div class="stat-label">总设备</div>
                    </div>
                    <div class="stat-card stat-normal">
                        <div class="stat-number" id="normalPoints">0</div>
                        <div class="stat-label">正常</div>
                    </div>
                    <div class="stat-card stat-warning">
                        <div class="stat-number" id="warningPoints">0</div>
                        <div class="stat-label">警告</div>
                    </div>
                    <div class="stat-card stat-error">
                        <div class="stat-number" id="errorPoints">0</div>
                        <div class="stat-label">异常</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 地图区域 -->
            <div class="map-container" id="mapContainer">
                <!-- 顶部悬浮工具栏 -->
                <div class="map-toolbar">
                    <div class="search-container">
                        <div class="search-icon">🔍</div>
                        <input type="text" class="search-box" placeholder="搜索设备名称、位置或状态..." id="searchBox">
                    </div>

                    <div class="toolbar-group">
                        <button class="toolbar-btn btn-secondary">设备筛选</button>
                        <button class="toolbar-btn btn-primary">应急预案</button>
                    </div>

                    <div class="zoom-controls">
                        <button class="zoom-btn" id="zoomOut">−</button>
                        <div class="zoom-display" id="zoomDisplay">100%</div>
                        <button class="zoom-btn" id="zoomIn">+</button>
                        <button class="reset-btn" id="resetView">重置</button>
                    </div>
                </div>

                <!-- 底部悬浮操作栏 -->
                <div class="bottom-floating-toolbar">
                    <div class="floating-actions">
                        <button class="floating-btn" title="实时监控">📹</button>
                        <button class="floating-btn" title="设备管理">🔧</button>
                        <button class="floating-btn" title="数据报表">📊</button>
                        <button class="floating-btn" title="系统设置">⚙️</button>
                    </div>

                    <div class="floating-version">
                        <span class="version-badge">v3.0</span>
                        <span>智能监控管理系统</span>
                    </div>
                </div>

                <div class="map-viewport" id="mapViewport">
                    <div class="floor-plan" id="floorPlan">
                        <!-- 加载指示器 -->
                        <div class="loading" id="loading">
                            <div class="loading-spinner"></div>
                            <div class="loading-text">正在加载设备数据...</div>
                        </div>
                    </div>
                </div>

                <!-- 图例 -->
                <div class="legend">
                    <div class="legend-title">设备状态</div>
                    <div class="legend-item">
                        <div class="legend-icon" style="background: #10b981;"></div>
                        <span>正常运行</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-icon" style="background: #f59e0b;"></div>
                        <span>警告状态</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-icon" style="background: #ef4444;"></div>
                        <span>异常/离线</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-icon" style="background: #64748b;"></div>
                        <span>维护模式</span>
                    </div>
                </div>

                <!-- 状态指示器 -->
                <div class="status-indicator" id="statusIndicator">
                    <div>拖动地图浏览 | 缩放: <span id="currentZoom">100%</span></div>
                </div>

                <!-- 悬浮提示 -->
                <div class="tooltip" id="tooltip"></div>
            </div>
        </div>
    </div>

    <!-- JavaScript 模块 -->
    <script type="module" src="./js/config.js"></script>
    <script type="module" src="./js/auth-manager.js"></script>
    <script type="module" src="./js/map-controller.js"></script>
    <script type="module" src="./js/device-manager.js"></script>
    <script type="module" src="./js/ui-utils.js"></script>
    <script type="module" src="./js/main.js"></script>
    <script src="./js/dialog-test.js"></script>
</body>
</html>