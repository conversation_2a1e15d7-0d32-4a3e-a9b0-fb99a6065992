/* login.css - 登录相关样式 */

/* 登录按钮样式 */
.login-btn {
    background: var(--primary-color);
    border: 1px solid var(--primary-color);
    color: white;
    padding: 8px 20px;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: var(--transition-fast);
    margin-left: 8px;
}

.login-btn:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-1px);
}

/* 用户信息显示 */
.user-info {
    display: none;
    align-items: center;
    gap: 12px;
    margin-left: 8px;
    padding: 8px 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
}

.user-avatar {
    width: 28px;
    height: 28px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: 600;
}

.user-name {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
}

.logout-btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 4px 12px;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 12px;
    transition: var(--transition-fast);
}

.logout-btn:hover {
    background: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

/* 登录弹窗样式 */
.modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-overlay.show {
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
}

.login-modal {
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    width: 400px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: transform 0.3s ease;
}

.modal-overlay.show .login-modal {
    transform: scale(1) translateY(0);
}

.modal-header {
    padding: 24px 24px 0;
    text-align: center;
}

.modal-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.modal-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 20px;
}

.modal-body {
    padding: 20px 24px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 6px;
}

.form-input {
    width: 100%;
    padding: 10px 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-size: 14px;
    background: var(--bg-primary);
    transition: var(--transition-fast);
    line-height: 1.5;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-input::placeholder {
    color: var(--text-secondary);
}

.modal-actions {
    padding: 0 24px 24px;
    display: flex;
    gap: 12px;
}

.modal-btn {
    flex: 1;
    padding: 12px 24px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition-fast);
}

.btn-cancel {
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.btn-cancel:hover {
    background: var(--text-secondary);
    color: white;
}

.btn-confirm {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-confirm:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-confirm:disabled {
    background: var(--border-color);
    border-color: var(--border-color);
    color: var(--text-secondary);
    cursor: not-allowed;
}

/* 错误提示 */
.error-message {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px 16px;
    border-radius: var(--radius-sm);
    font-size: 13px;
    margin-bottom: 16px;
    display: none;
}

.error-message.show {
    display: block;
}

/* 成功提示 */
.success-message {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #166534;
    padding: 12px 16px;
    border-radius: var(--radius-sm);
    font-size: 13px;
    margin-bottom: 16px;
    display: none;
}

.success-message.show {
    display: block;
}