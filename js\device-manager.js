/**
 * device-manager.js - 设备管理模块
 * 负责设备点的生成、显示和交互
 */

import CONFIG from './config.js';
import { showTooltip, hideTooltip, updateStatistics } from './ui-utils.js';

class DeviceManager {
    constructor() {
        // 元素引用
        this.mapView = document.getElementById('mapViewport'); // 更新为实际ID
        this.searchInput = document.getElementById('searchBox'); // 更新为实际ID

        // 添加null检查
        if (!this.mapView) {
            console.error('地图视图元素未找到');
            return;
        }
        
        // 设备数据
        this.devices = [];
        this.deviceElements = [];
        this.statistics = {
            total: 0,
            normal: 0,
            warning: 0,
            error: 0,
            offline: 0
        };
        
        // 绑定方法到实例
        this.generateDevices = this.generateDevices.bind(this);
        this.handleDeviceMouseEnter = this.handleDeviceMouseEnter.bind(this);
        this.handleDeviceMouseLeave = this.handleDeviceMouseLeave.bind(this);
        this.handleSearch = this.handleSearch.bind(this);
        
        // 初始化事件监听
        this.initEventListeners();
    }
    
    /**
     * 初始化事件监听
     */
    initEventListeners() {
        // 搜索输入防抖
        if (this.searchInput) {
            this.searchInput.addEventListener('input', this.debounce(this.handleSearch, 300));
        }
    }
    
    /**
     * 生成随机设备点
     * @param {number} count - 设备数量
     */
    generateDevices(count = 100) {
        // 确保mapView存在
        if (!this.mapView) return;

        // 清空现有设备
        this.devices = [];
        this.deviceElements = [];
        this.statistics = {
            total: 0,
            normal: 0,
            warning: 0,
            error: 0,
            offline: 0
        };
        
        // 清空地图视图
        while (this.mapView.firstChild) {
            this.mapView.removeChild(this.mapView.firstChild);
        }
        
        // 生成新设备
        for (let i = 0; i < count; i++) {
            this.createDevice();
        }
        
        // 更新统计信息
        updateStatistics(this.statistics);
    }
    
    /**
     * 创建单个设备
     */
    createDevice() {
        // 随机设备类型
        const deviceTypes = Object.values(CONFIG.DEVICE_TYPES);
        const type = deviceTypes[Math.floor(Math.random() * deviceTypes.length)];
        
        // 随机设备状态（基于权重）
        const status = this.getRandomStatus();
        
        // 随机位置
        const x = Math.random() * 100;
        const y = Math.random() * 100;
        
        // 随机设备名称
        const namePrefix = CONFIG.MOCK.DEVICE_NAME_PREFIXES[type];
        const name = `${namePrefix[Math.floor(Math.random() * namePrefix.length)]}-${Math.floor(Math.random() * 1000)}`;
        
        // 随机位置名称
        const locationPrefix = CONFIG.MOCK.LOCATION_PREFIXES;
        const location = `${locationPrefix[Math.floor(Math.random() * locationPrefix.length)]}-${Math.floor(Math.random() * 100)}`;
        
        // 创建设备数据
        const device = {
            id: `device-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
            name,
            type,
            status,
            location,
            x: `${x}%`,
            y: `${y}%`,
            lastUpdate: new Date().toLocaleString()
        };
        
        // 添加到设备列表
        this.devices.push(device);
        
        // 创建设备DOM元素
        const deviceElement = document.createElement('div');
        deviceElement.className = `monitor-point device-${type}`;
        deviceElement.style.left = device.x;
        deviceElement.style.top = device.y;
        deviceElement.dataset.id = device.id;
        deviceElement.dataset.type = device.type;
        deviceElement.dataset.status = device.status;
        deviceElement.dataset.name = device.name;
        deviceElement.dataset.location = device.location;
        
        // 创建SVG图标
        deviceElement.innerHTML = this.createSVGIcon(device.type, device.status);
        
        // 添加事件监听
        deviceElement.addEventListener('mouseenter', (e) => this.handleDeviceMouseEnter(e, device));
        deviceElement.addEventListener('mouseleave', this.handleDeviceMouseLeave);
        
        // 添加到地图
        this.mapView.appendChild(deviceElement);
        this.deviceElements.push(deviceElement);
        
        // 更新统计信息
        this.statistics.total++;
        this.statistics[status]++;
        
        return device;
    }
    
    /**
     * 获取随机状态（基于权重）
     * @returns {string} 设备状态
     */
    getRandomStatus() {
        const weights = CONFIG.STATUS_WEIGHTS;
        const statuses = Object.keys(weights);
        const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
        
        let random = Math.random() * totalWeight;
        let cumulativeWeight = 0;
        
        for (const status of statuses) {
            cumulativeWeight += weights[status];
            if (random < cumulativeWeight) {
                return status;
            }
        }
        
        return 'normal'; // 默认状态
    }
    
    /**
     * 创建SVG图标
     * @param {string} type - 设备类型
     * @param {string} status - 设备状态
     * @returns {string} SVG HTML字符串
     */
    createSVGIcon(type, status) {
        let path = '';
        
        // 根据设备类型设置不同的图标路径
        switch (type) {
            case 'camera':
                path = 'M12 9a3 3 0 100 6 3 3 0 000-6zM2 9a2 2 0 012-2h1a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V9z M14 10a2 2 0 012-2h1a2 2 0 012 2v6a2 2 0 01-2 2h-1a2 2 0 01-2-2v-6z';
                break;
            case 'sensor':
                path = 'M13 10V3L4 14h7v7l9-11h-7z';
                break;
            case 'alarm':
                path = 'M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z';
                break;
            case 'detector':
                path = 'M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 100-2 1 1 0 000 2zm7-1a1 1 0 11-2 0 1 1 0 012 0zm-.464 5.535a1 1 0 10-1.415-1.414 3 3 0 01-4.242 0 1 1 0 00-1.415 1.414 5 5 0 007.072 0z';
                break;
            default:
                path = 'M10 18a8 8 0 100-16 8 8 0 000 16z';
        }
        
        // 添加脉冲效果（仅对警告和错误状态）
        const pulse = (status === 'warning' || status === 'error') ? 
            `<div class="pulse" style="color: var(--${status === 'warning' ? 'warning' : 'danger'}-color)"></div>` : '';
        
        return `
            ${pulse}
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="status-${status}">
                <path fill-rule="evenodd" d="${path}" clip-rule="evenodd" />
            </svg>
        `;
    }
    
    /**
     * 处理设备鼠标进入事件
     * @param {MouseEvent} e - 鼠标事件
     * @param {Object} device - 设备数据
     */
    handleDeviceMouseEnter(e, device) {
        const element = e.currentTarget;
        
        // 构建工具提示内容
        const tooltipContent = `
            <div class="tooltip-title">${device.name}</div>
            <div class="tooltip-content">
                <div class="tooltip-row">
                    <span class="tooltip-label">类型:</span>
                    <span class="tooltip-value">${this.getDeviceTypeName(device.type)}</span>
                </div>
                <div class="tooltip-row">
                    <span class="tooltip-label">状态:</span>
                    <span class="tooltip-value ${device.status}">${this.getStatusName(device.status)}</span>
                </div>
                <div class="tooltip-row">
                    <span class="tooltip-label">位置:</span>
                    <span class="tooltip-value">${device.location}</span>
                </div>
                <div class="tooltip-row">
                    <span class="tooltip-label">最后更新:</span>
                    <span class="tooltip-value">${device.lastUpdate}</span>
                </div>
            </div>
        `;
        
        // 显示工具提示
        showTooltip(e, tooltipContent);
    }
    
    /**
     * 处理设备鼠标离开事件
     */
    handleDeviceMouseLeave() {
        hideTooltip();
    }
    
    /**
     * 处理搜索
     * @param {Event} e - 输入事件
     */
    handleSearch(e) {
        const searchTerm = e.target.value.trim().toLowerCase();
        
        if (!searchTerm) {
            // 如果搜索词为空，显示所有设备
            this.deviceElements.forEach(element => {
                element.style.display = 'block';
            });
            return;
        }
        
        // 过滤设备
        this.deviceElements.forEach(element => {
            const name = element.dataset.name.toLowerCase();
            const type = element.dataset.type.toLowerCase();
            const status = element.dataset.status.toLowerCase();
            const location = element.dataset.location.toLowerCase();
            
            // 检查是否匹配搜索词
            const isMatch = 
                name.includes(searchTerm) || 
                type.includes(searchTerm) || 
                status.includes(searchTerm) || 
                location.includes(searchTerm) ||
                this.getDeviceTypeName(type).toLowerCase().includes(searchTerm) ||
                this.getStatusName(status).toLowerCase().includes(searchTerm);
            
            // 设置显示状态
            element.style.display = isMatch ? 'block' : 'none';
        });
    }
    
    /**
     * 获取设备类型名称
     * @param {string} type - 设备类型代码
     * @returns {string} 设备类型名称
     */
    getDeviceTypeName(type) {
        const typeNames = {
            camera: '摄像头',
            sensor: '传感器',
            alarm: '报警器',
            detector: '探测器'
        };
        
        return typeNames[type] || type;
    }
    
    /**
     * 获取状态名称
     * @param {string} status - 状态代码
     * @returns {string} 状态名称
     */
    getStatusName(status) {
        const statusNames = {
            normal: '正常',
            warning: '警告',
            error: '错误',
            offline: '离线'
        };
        
        return statusNames[status] || status;
    }
    
    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间（毫秒）
     * @returns {Function} 防抖后的函数
     */
    debounce(func, wait) {
        let timeout;
        return function(...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }
}

export default DeviceManager;