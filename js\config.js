/**
 * config.js - 系统配置文件
 * 包含系统的基本配置信息和常量定义
 */

const CONFIG = {
    // 系统信息
    SYSTEM: {
        NAME: "智能监控系统",
        VERSION: "v1.0.0",
        ADMIN_URL: "./admin.html"
    },

    // 认证相关
    AUTH: {
        TOKEN_KEY: "monitor_auth_token",
        TOKEN_EXPIRY: 24 * 60 * 60 * 1000, // 24小时
        LOGIN_API_URL: "/api/login" // 模拟API地址
    },

    // 设备类型
    DEVICE_TYPES: {
        CAMERA: "camera",
        SENSOR: "sensor",
        ALARM: "alarm",
        DETECTOR: "detector"
    },

    // 设备状态
    DEVICE_STATUS: {
        NORMAL: "normal",
        WARNING: "warning",
        ERROR: "error",
        OFFLINE: "offline"
    },

    // 状态概率权重（用于随机生成状态）
    STATUS_WEIGHTS: {
        normal: 70,
        warning: 15,
        error: 10,
        offline: 5
    },

    // 地图相关
    MAP: {
        DEFAULT_ZOOM: 1,
        MIN_ZOOM: 1.0, // 最小100%
        MAX_ZOOM: 6.0, // 最大600%
        ZOOM_STEP: 0.2, // 提升至2倍缩放速度
        ANIMATION_DURATION: 300
    },

    // 模拟数据
    MOCK: {
        // 模拟用户数据库
        USERS: [
            { username: "admin", password: "admin123", name: "管理员", role: "admin" },
            { username: "manager", password: "manager123", name: "经理", role: "manager" },
            { username: "test", password: "test123", name: "测试用户", role: "user" }
        ],

        // 楼层数据
        FLOORS: [
            { id: "f1", name: "一楼", deviceCount: 42 },
            { id: "f2", name: "二楼", deviceCount: 38 },
            { id: "f3", name: "三楼", deviceCount: 45 },
            { id: "f4", name: "四楼", deviceCount: 30 },
            { id: "f5", name: "五楼", deviceCount: 25 }
        ],

        // 设备名称前缀（用于随机生成）
        DEVICE_NAME_PREFIXES: {
            camera: ["高清摄像头", "球机", "枪机", "红外摄像头"],
            sensor: ["温度传感器", "湿度传感器", "烟雾传感器", "气体传感器"],
            alarm: ["声光报警器", "警报器", "紧急按钮", "报警装置"],
            detector: ["人体探测器", "运动检测器", "入侵检测器", "红外探测器"]
        },

        // 设备位置前缀（用于随机生成）
        LOCATION_PREFIXES: [
            "大厅", "走廊", "办公室", "会议室", "电梯口",
            "安全出口", "楼梯间", "设备间", "休息区", "门口"
        ]
    }
};

// 导出配置对象
export default CONFIG;