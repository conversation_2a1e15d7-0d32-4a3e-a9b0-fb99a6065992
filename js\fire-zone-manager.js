/**
 * fire-zone-manager.js - 防火分区管理器
 * 处理防火分区的绘制、编辑和管理
 */

import { showStatusMessage } from './ui-utils.js';

class FireZoneManager {
    constructor(mapController) {
        this.mapController = mapController;
        this.fireZones = [];
        this.isDrawing = false;
        this.currentShape = null;
        this.drawingMode = 'rectangle'; // rectangle, circle, polygon
        this.currentPoints = [];
        this.selectedZone = null;
    }

    /**
     * 开始绘制防火分区
     * @param {Object} startPosition - 起始位置
     */
    startDrawing(startPosition) {
        console.log('开始绘制防火分区');

        // 直接开始多边形绘制模式
        this.drawingMode = 'polygon';
        this.isDrawing = true;
        this.currentPoints = [];

        // 设置绘制样式
        this.setDrawingCursor();

        // 添加绘制事件监听
        this.addDrawingListeners();

        // 创建坐标虚线
        this.createCrosshair();

        console.log('绘制状态设置完成，isDrawing:', this.isDrawing);

        showStatusMessage('🔥 防火分区绘制模式：点击设置点位（最少3个点），右键完成绘制');
    }

    /**
     * 设置绘制光标
     */
    setDrawingCursor() {
        const mapContainer = document.getElementById('mapContainer');
        if (mapContainer) {
            mapContainer.style.cursor = 'crosshair';
            mapContainer.classList.add('drawing-mode');
        }
    }

    /**
     * 恢复默认光标
     */
    resetCursor() {
        const mapContainer = document.getElementById('mapContainer');
        if (mapContainer) {
            mapContainer.style.cursor = '';
            mapContainer.classList.remove('drawing-mode');
        }
    }

    /**
     * 创建坐标虚线
     */
    createCrosshair() {
        const mapViewport = document.getElementById('mapViewport');
        if (!mapViewport) return;

        // 创建十字线容器
        this.crosshairContainer = document.createElement('div');
        this.crosshairContainer.className = 'crosshair-container';
        this.crosshairContainer.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        `;

        // 创建水平线
        this.horizontalLine = document.createElement('div');
        this.horizontalLine.className = 'crosshair-horizontal';
        this.horizontalLine.style.cssText = `
            position: absolute;
            width: 100%;
            height: 1px;
            background: #ef4444;
            border-top: 1px dashed #ef4444;
            opacity: 0.7;
            display: none;
        `;

        // 创建垂直线
        this.verticalLine = document.createElement('div');
        this.verticalLine.className = 'crosshair-vertical';
        this.verticalLine.style.cssText = `
            position: absolute;
            width: 1px;
            height: 100%;
            background: #ef4444;
            border-left: 1px dashed #ef4444;
            opacity: 0.7;
            display: none;
        `;

        // 创建坐标显示
        this.coordinateDisplay = document.createElement('div');
        this.coordinateDisplay.className = 'coordinate-display';
        this.coordinateDisplay.style.cssText = `
            position: absolute;
            background: rgba(239, 68, 68, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-family: monospace;
            pointer-events: none;
            z-index: 1001;
            display: none;
        `;

        this.crosshairContainer.appendChild(this.horizontalLine);
        this.crosshairContainer.appendChild(this.verticalLine);
        this.crosshairContainer.appendChild(this.coordinateDisplay);
        mapViewport.appendChild(this.crosshairContainer);
    }

    /**
     * 更新坐标虚线位置
     */
    updateCrosshair(x, y) {
        if (!this.crosshairContainer) return;

        this.horizontalLine.style.top = `${y}px`;
        this.horizontalLine.style.display = 'block';

        this.verticalLine.style.left = `${x}px`;
        this.verticalLine.style.display = 'block';

        this.coordinateDisplay.style.left = `${x + 10}px`;
        this.coordinateDisplay.style.top = `${y - 25}px`;
        this.coordinateDisplay.style.display = 'block';
        this.coordinateDisplay.textContent = `(${Math.round(x)}, ${Math.round(y)})`;
    }

    /**
     * 清除坐标虚线
     */
    clearCrosshair() {
        if (this.crosshairContainer && this.crosshairContainer.parentNode) {
            this.crosshairContainer.parentNode.removeChild(this.crosshairContainer);
        }
        this.crosshairContainer = null;
        this.horizontalLine = null;
        this.verticalLine = null;
        this.coordinateDisplay = null;
    }

    /**
     * 显示绘制模式选择对话框
     * @param {Function} onSelect - 选择回调
     */
    showDrawingModeDialog(onSelect) {
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay drawing-mode-modal';
        overlay.style.cssText = `
            display: flex !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(0,0,0,0.5) !important;
            z-index: 1000 !important;
            justify-content: center !important;
            align-items: center !important;
        `;

        const modal = document.createElement('div');
        modal.style.cssText = `
            background: #fff !important;
            padding: 24px !important;
            border-radius: 8px !important;
            min-width: 400px !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3) !important;
        `;

        modal.innerHTML = `
            <div class="modal-header" style="margin-bottom: 20px;">
                <h2 style="margin: 0; color: #333;">选择防火分区形状</h2>
                <p style="margin: 8px 0 0 0; color: #666; font-size: 14px;">
                    选择要绘制的防火分区形状类型
                </p>
            </div>

            <div class="shape-options" style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                <div class="shape-option" data-shape="rectangle" style="
                    padding: 20px; border: 2px solid #e2e8f0; border-radius: 8px; cursor: pointer;
                    text-align: center; transition: all 0.2s ease;
                ">
                    <div style="font-size: 32px; margin-bottom: 8px;">⬜</div>
                    <div style="font-weight: 500;">矩形区域</div>
                    <div style="font-size: 12px; color: #666;">点击两个对角点</div>
                </div>

                <div class="shape-option" data-shape="circle" style="
                    padding: 20px; border: 2px solid #e2e8f0; border-radius: 8px; cursor: pointer;
                    text-align: center; transition: all 0.2s ease;
                ">
                    <div style="font-size: 32px; margin-bottom: 8px;">⭕</div>
                    <div style="font-weight: 500;">圆形区域</div>
                    <div style="font-size: 12px; color: #666;">点击中心和边缘</div>
                </div>

                <div class="shape-option" data-shape="polygon" style="
                    padding: 20px; border: 2px solid #e2e8f0; border-radius: 8px; cursor: pointer;
                    text-align: center; transition: all 0.2s ease; grid-column: 1 / -1;
                ">
                    <div style="font-size: 32px; margin-bottom: 8px;">🔷</div>
                    <div style="font-weight: 500;">多边形区域</div>
                    <div style="font-size: 12px; color: #666;">点击多个点，双击完成</div>
                </div>
            </div>

            <div style="margin-top: 20px; text-align: right;">
                <button id="cancelDrawing" style="
                    background: #6b7280; color: white; border: none; padding: 10px 20px;
                    border-radius: 6px; cursor: pointer; font-size: 14px;
                ">取消</button>
            </div>
        `;

        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // 绑定事件
        modal.querySelectorAll('.shape-option').forEach(option => {
            option.addEventListener('mouseenter', () => {
                option.style.borderColor = '#3b82f6';
                option.style.backgroundColor = '#f8fafc';
            });

            option.addEventListener('mouseleave', () => {
                option.style.borderColor = '#e2e8f0';
                option.style.backgroundColor = 'transparent';
            });

            option.addEventListener('click', () => {
                const shape = option.dataset.shape;
                overlay.remove();
                onSelect(shape);
            });
        });

        modal.querySelector('#cancelDrawing').addEventListener('click', () => {
            overlay.remove();
        });

        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
            }
        });
    }

    /**
     * 添加绘制事件监听
     */
    addDrawingListeners() {
        const mapContainer = document.getElementById('mapContainer');
        if (!mapContainer) {
            console.error('找不到mapContainer元素');
            return;
        }

        console.log('添加绘制事件监听器');

        this.drawingClickHandler = (e) => this.handleDrawingClick(e);
        this.drawingRightClickHandler = (e) => this.handleDrawingRightClick(e);
        this.drawingMouseMoveHandler = (e) => this.handleDrawingMouseMove(e);
        this.drawingKeyHandler = (e) => this.handleDrawingKey(e);

        mapContainer.addEventListener('click', this.drawingClickHandler, true);
        mapContainer.addEventListener('contextmenu', this.drawingRightClickHandler, true);
        mapContainer.addEventListener('mousemove', this.drawingMouseMoveHandler);
        document.addEventListener('keydown', this.drawingKeyHandler);

        console.log('绘制事件监听器添加完成');
    }

    /**
     * 移除绘制事件监听
     */
    removeDrawingListeners() {
        const mapContainer = document.getElementById('mapContainer');
        if (!mapContainer) return;

        console.log('移除绘制事件监听器');

        mapContainer.removeEventListener('click', this.drawingClickHandler, true);
        mapContainer.removeEventListener('contextmenu', this.drawingRightClickHandler, true);
        mapContainer.removeEventListener('mousemove', this.drawingMouseMoveHandler);
        document.removeEventListener('keydown', this.drawingKeyHandler);
    }

    /**
     * 处理绘制点击事件（左键添加点位）
     * @param {MouseEvent} e - 鼠标事件
     */
    handleDrawingClick(e) {
        if (!this.isDrawing || e.button !== 0) return; // 只处理左键

        e.preventDefault();
        e.stopPropagation();

        const rect = e.currentTarget.getBoundingClientRect();
        const position = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };

        console.log('添加点位:', position, '当前点位数量:', this.currentPoints.length);

        // 添加点位
        this.currentPoints.push(position);
        this.updatePreview();

        // 更新状态提示
        if (this.currentPoints.length < 3) {
            showStatusMessage(`🔥 已添加${this.currentPoints.length}个点位，还需${3 - this.currentPoints.length}个点位（右键完成绘制）`);
        } else {
            showStatusMessage(`🔥 已添加${this.currentPoints.length}个点位，右键完成绘制`);
        }
    }

    /**
     * 处理绘制右键事件（完成绘制）
     * @param {MouseEvent} e - 鼠标事件
     */
    handleDrawingRightClick(e) {
        if (!this.isDrawing) return;

        e.preventDefault();
        e.stopPropagation();

        console.log('右键完成绘制，当前点位数量:', this.currentPoints.length);

        // 检查是否有足够的点位
        if (this.currentPoints.length < 3) {
            showStatusMessage('⚠️ 至少需要3个点位才能完成绘制');
            return;
        }

        // 完成绘制
        this.finishDrawing();
    }

    /**
     * 处理矩形绘制
     * @param {Object} position - 位置
     */
    handleRectangleDrawing(position) {
        if (this.currentPoints.length === 1) {
            this.currentPoints.push(position);
            this.finishDrawing();
        }
    }

    /**
     * 处理圆形绘制
     * @param {Object} position - 位置
     */
    handleCircleDrawing(position) {
        if (this.currentPoints.length === 1) {
            this.currentPoints.push(position);
            this.finishDrawing();
        }
    }

    /**
     * 处理多边形绘制
     * @param {Object} position - 位置
     */
    handlePolygonDrawing(position) {
        // 检查是否是双击（点击位置很接近上一个点）
        if (this.currentPoints.length > 0) {
            const lastPoint = this.currentPoints[this.currentPoints.length - 1];
            const distance = Math.sqrt(
                Math.pow(position.x - lastPoint.x, 2) + Math.pow(position.y - lastPoint.y, 2)
            );

            // 如果距离小于10像素，认为是双击，完成绘制
            if (distance < 10 && this.currentPoints.length >= 3) {
                console.log('检测到双击，完成多边形绘制');
                this.finishDrawing();
                return;
            }
        }

        this.currentPoints.push(position);
        this.updatePreview();

        console.log('多边形点位数量:', this.currentPoints.length);

        // 提示用户操作
        if (this.currentPoints.length === 2) {
            showStatusMessage('继续点击添加更多点，双击或按Enter完成绘制');
        } else if (this.currentPoints.length >= 3) {
            showStatusMessage(`已添加${this.currentPoints.length}个点，双击或按Enter完成绘制`);
        }
    }

    /**
     * 处理绘制鼠标移动
     * @param {MouseEvent} e - 鼠标事件
     */
    handleDrawingMouseMove(e) {
        if (!this.isDrawing) return;

        const rect = e.currentTarget.getBoundingClientRect();
        const currentPos = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };

        // 更新坐标虚线
        this.updateCrosshair(currentPos.x, currentPos.y);

        // 更新预览
        this.updatePreview(currentPos);
    }

    /**
     * 处理绘制键盘事件
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleDrawingKey(e) {
        if (!this.isDrawing) return;

        if (e.key === 'Escape') {
            this.cancelDrawing();
        } else if (e.key === 'Enter' && this.drawingMode === 'polygon') {
            this.finishDrawing();
        }
    }

    /**
     * 完成绘制
     */
    finishDrawing() {
        if (this.currentPoints.length < 3) {
            showStatusMessage('至少需要3个点才能完成绘制');
            return;
        }

        console.log('完成防火分区绘制，点位数量:', this.currentPoints.length);

        // 计算面积
        const area = this.calculatePolygonArea(this.currentPoints);

        // 创建防火分区数据
        const zoneData = {
            id: `fire-zone-${Date.now()}`,
            name: '新防火分区',
            shape: 'polygon',
            points: [...this.currentPoints], // 复制数组，避免引用问题
            area: area,
            fireRating: 'A',
            maxOccupancy: 100,
            exitCount: 2,
            sprinklerSystem: true,
            smokeDetection: true,
            fireAlarm: false,
            fireExtinguisher: false,
            emergencyLighting: false,
            ventilationSystem: false,
            riskLevel: 'medium',
            inspectionCycle: 30,
            evacuationDistance: 30,
            description: '',
            createTime: Date.now()
        };

        console.log('防火分区数据:', zoneData);

        // 先清理绘制状态
        this.resetDrawing();

        // 显示配置对话框
        this.showZoneConfigDialog(zoneData, (configuredZone) => {
            console.log('配置完成的防火分区:', configuredZone);
            this.fireZones.push(configuredZone);
            this.renderFireZone(configuredZone);
            this.saveFireZone(configuredZone);
            showStatusMessage(`防火分区 ${configuredZone.name} 已创建`);
        });
    }

    /**
     * 取消绘制
     */
    cancelDrawing() {
        this.resetDrawing();
        showStatusMessage('已取消绘制');
    }

    /**
     * 重置绘制状态
     */
    resetDrawing() {
        this.isDrawing = false;
        this.currentPoints = [];
        this.removeDrawingListeners();
        this.clearPreview();
        this.clearCrosshair();
        this.resetCursor();

        console.log('绘制状态已重置');
    }

    /**
     * 计算多边形面积
     * @param {Array} points - 点位数组
     * @returns {number} 面积
     */
    calculatePolygonArea(points) {
        if (points.length < 3) return 0;

        // 使用鞋带公式计算多边形面积
        let area = 0;
        for (let i = 0; i < points.length; i++) {
            const j = (i + 1) % points.length;
            area += points[i].x * points[j].y;
            area -= points[j].x * points[i].y;
        }
        return Math.abs(area) / 2;
    }

    /**
     * 计算区域面积
     * @returns {number} 面积
     */
    calculateArea() {
        if (this.drawingMode === 'rectangle' && this.currentPoints.length === 2) {
            const width = Math.abs(this.currentPoints[1].x - this.currentPoints[0].x);
            const height = Math.abs(this.currentPoints[1].y - this.currentPoints[0].y);
            return width * height;
        } else if (this.drawingMode === 'circle' && this.currentPoints.length === 2) {
            const radius = Math.sqrt(
                Math.pow(this.currentPoints[1].x - this.currentPoints[0].x, 2) +
                Math.pow(this.currentPoints[1].y - this.currentPoints[0].y, 2)
            );
            return Math.PI * radius * radius;
        } else if (this.drawingMode === 'polygon') {
            // 使用鞋带公式计算多边形面积
            let area = 0;
            const points = this.currentPoints;
            for (let i = 0; i < points.length; i++) {
                const j = (i + 1) % points.length;
                area += points[i].x * points[j].y;
                area -= points[j].x * points[i].y;
            }
            return Math.abs(area) / 2;
        }
        return 0;
    }

    /**
     * 获取形状名称
     * @param {string} shape - 形状类型
     * @returns {string} 形状名称
     */
    getShapeName(shape) {
        const names = {
            rectangle: '矩形',
            circle: '圆形',
            polygon: '多边形'
        };
        return names[shape] || '';
    }

    /**
     * 显示防火分区配置对话框
     * @param {Object} zoneData - 分区数据
     * @param {Function} onConfirm - 确认回调
     */
    showZoneConfigDialog(zoneData, onConfirm) {
        console.log('显示防火分区配置对话框', zoneData);

        // 使用简化版本避免模板字符串错误
        this.showSimpleZoneConfigDialog(zoneData, onConfirm);

        try {
            const overlay = document.createElement('div');
        overlay.className = 'modal-overlay fire-zone-config-modal';
        overlay.style.cssText = `
            display: flex !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background-color: rgba(0,0,0,0.5) !important;
            z-index: 9999 !important;
            justify-content: center !important;
            align-items: center !important;
        `;

        const modal = document.createElement('div');
        modal.style.cssText = `
            background: #fff !important;
            padding: 24px !important;
            border-radius: 8px !important;
            min-width: 600px !important;
            max-width: 700px !important;
            max-height: 80vh !important;
            overflow-y: auto !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3) !important;
        `;

        modal.innerHTML = `
            <div class="modal-header" style="margin-bottom: 20px;">
                <h2 style="margin: 0; color: #333;">🔥 防火分区配置</h2>
                <p style="margin: 8px 0 0 0; color: #666; font-size: 14px;">
                    配置防火分区的安全参数和消防设施
                </p>
            </div>

            <div class="modal-body">
                <!-- 基本信息 -->
                <div class="form-section" style="margin-bottom: 24px;">
                    <h3 style="margin: 0 0 16px 0; font-size: 16px; color: #333; border-bottom: 2px solid #ef4444; padding-bottom: 8px;">基本信息</h3>

                    <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                        <div class="form-group" style="flex: 2;">
                            <label style="display: block; margin-bottom: 4px; font-weight: 500;">分区名称</label>
                            <input type="text" id="zoneName" value="${zoneData.name}" style="
                                width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                                font-size: 14px; box-sizing: border-box;
                            ">
                        </div>
                        <div class="form-group" style="flex: 1;">
                            <label style="display: block; margin-bottom: 4px; font-weight: 500;">防火等级</label>
                            <select id="fireRating" style="
                                width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                                font-size: 14px; box-sizing: border-box;
                            ">
                                <option value="A" ${zoneData.fireRating === 'A' ? 'selected' : ''}>A级 (最高)</option>
                                <option value="B" ${zoneData.fireRating === 'B' ? 'selected' : ''}>B级 (高)</option>
                                <option value="C" ${zoneData.fireRating === 'C' ? 'selected' : ''}>C级 (中)</option>
                                <option value="D" ${zoneData.fireRating === 'D' ? 'selected' : ''}>D级 (低)</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                        <div class="form-group" style="flex: 1;">
                            <label style="display: block; margin-bottom: 4px; font-weight: 500;">最大容量 (人)</label>
                            <input type="number" id="maxOccupancy" value="${zoneData.maxOccupancy}" min="1" style="
                                width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                                font-size: 14px; box-sizing: border-box;
                            ">
                        </div>
                        <div class="form-group" style="flex: 1;">
                            <label style="display: block; margin-bottom: 4px; font-weight: 500;">安全出口数量</label>
                            <input type="number" id="exitCount" value="${zoneData.exitCount}" min="1" style="
                                width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                                font-size: 14px; box-sizing: border-box;
                            ">
                        </div>
                        <div class="form-group" style="flex: 1;">
                            <label style="display: block; margin-bottom: 4px; font-weight: 500;">疏散距离 (米)</label>
                            <input type="number" id="evacuationDistance" value="${zoneData.evacuationDistance || 30}" min="1" style="
                                width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                                font-size: 14px; box-sizing: border-box;
                            ">
                        </div>
                    </div>
                </div>

                <!-- 消防设施 -->
                <div class="form-section" style="margin-bottom: 24px;">
                    <h3 style="margin: 0 0 16px 0; font-size: 16px; color: #333; border-bottom: 2px solid #ef4444; padding-bottom: 8px;">消防设施</h3>

                    <div class="form-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                        <div class="form-group">
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="sprinklerSystem" ${zoneData.sprinklerSystem ? 'checked' : ''} style="
                                    width: 16px; height: 16px; cursor: pointer;
                                ">
                                <span style="font-weight: 500;">🚿 自动喷淋系统</span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="smokeDetection" ${zoneData.smokeDetection ? 'checked' : ''} style="
                                    width: 16px; height: 16px; cursor: pointer;
                                ">
                                <span style="font-weight: 500;">💨 烟雾探测器</span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="fireAlarm" ${zoneData.fireAlarm || false ? 'checked' : ''} style="
                                    width: 16px; height: 16px; cursor: pointer;
                                ">
                                <span style="font-weight: 500;">🚨 火灾报警器</span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="fireExtinguisher" ${zoneData.fireExtinguisher || false ? 'checked' : ''} style="
                                    width: 16px; height: 16px; cursor: pointer;
                                ">
                                <span style="font-weight: 500;">🧯 灭火器</span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="emergencyLighting" ${zoneData.emergencyLighting || false ? 'checked' : ''} style="
                                    width: 16px; height: 16px; cursor: pointer;
                                ">
                                <span style="font-weight: 500;">💡 应急照明</span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="ventilationSystem" ${zoneData.ventilationSystem || false ? 'checked' : ''} style="
                                    width: 16px; height: 16px; cursor: pointer;
                                ">
                                <span style="font-weight: 500;">🌪️ 排烟系统</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 风险评估 -->
                <div class="form-section" style="margin-bottom: 24px;">
                    <h3 style="margin: 0 0 16px 0; font-size: 16px; color: #333; border-bottom: 2px solid #ef4444; padding-bottom: 8px;">风险评估</h3>

                    <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                        <div class="form-group" style="flex: 1;">
                            <label style="display: block; margin-bottom: 4px; font-weight: 500;">火灾风险等级</label>
                            <select id="riskLevel" style="
                                width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                                font-size: 14px; box-sizing: border-box;
                            ">
                                <option value="low" ${(zoneData.riskLevel || 'medium') === 'low' ? 'selected' : ''}>🟢 低风险</option>
                                <option value="medium" ${(zoneData.riskLevel || 'medium') === 'medium' ? 'selected' : ''}>🟡 中风险</option>
                                <option value="high" ${(zoneData.riskLevel || 'medium') === 'high' ? 'selected' : ''}>🟠 高风险</option>
                                <option value="critical" ${(zoneData.riskLevel || 'medium') === 'critical' ? 'selected' : ''}>🔴 极高风险</option>
                            </select>
                        </div>
                        <div class="form-group" style="flex: 1;">
                            <label style="display: block; margin-bottom: 4px; font-weight: 500;">检查周期 (天)</label>
                            <input type="number" id="inspectionCycle" value="${zoneData.inspectionCycle || 30}" min="1" style="
                                width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                                font-size: 14px; box-sizing: border-box;
                            ">
                        </div>
                    </div>

                    <div class="form-group" style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 4px; font-weight: 500;">备注说明</label>
                        <textarea id="zoneDescription" rows="3" placeholder="输入防火分区的详细说明..." style="
                            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                            font-size: 14px; resize: vertical; box-sizing: border-box;
                        ">${zoneData.description}</textarea>
                    </div>
                </div>

                <!-- 区域信息 -->
                <div class="info-section" style="background: #fef2f2; padding: 16px; border-radius: 6px; border-left: 4px solid #ef4444;">
                    <h4 style="margin: 0 0 12px 0; font-size: 14px; color: #374151;">📊 区域信息</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 16px; font-size: 12px; color: #6b7280;">
                        <div>
                            <strong>形状:</strong> ${zoneData.shape === 'polygon' ? '多边形' : zoneData.shape === 'rectangle' ? '矩形' : zoneData.shape === 'circle' ? '圆形' : zoneData.shape}
                        </div>
                        <div>
                            <strong>面积:</strong> ${Math.round(zoneData.area)} 平方像素
                        </div>
                        <div>
                            <strong>创建时间:</strong> ${new Date(zoneData.createTime).toLocaleString()}
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-actions" style="margin-top: 24px; text-align: right; display: flex; gap: 12px; justify-content: flex-end;">
                <button id="cancelZoneBtn" style="
                    background: #6b7280; color: white; border: none; padding: 10px 20px;
                    border-radius: 6px; cursor: pointer; font-size: 14px;
                ">取消</button>
                <button id="confirmZoneBtn" style="
                    background: #ef4444; color: white; border: none; padding: 10px 20px;
                    border-radius: 6px; cursor: pointer; font-size: 14px;
                ">确认添加</button>
            </div>
        `;

        overlay.appendChild(modal);

        // 确保移除任何现有的配置对话框
        const existingModals = document.querySelectorAll('.fire-zone-config-modal');
        existingModals.forEach(modal => modal.remove());

        console.log('准备添加配置对话框到DOM');
        document.body.appendChild(overlay);
        console.log('配置对话框已添加到DOM，元素:', overlay);
        console.log('对话框是否可见:', overlay.offsetWidth > 0 && overlay.offsetHeight > 0);

            this.bindZoneConfigEvents(overlay, zoneData, onConfirm);
        } catch (error) {
            console.error('显示防火分区配置对话框时出错:', error);
            alert('显示配置对话框时出错，请查看控制台了解详情');
        }
    }

    /**
     * 显示简化的防火分区配置对话框
     */
    showSimpleZoneConfigDialog(zoneData, onConfirm) {
        console.log('显示简化防火分区配置对话框');

        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay fire-zone-config-modal';
        overlay.style.cssText = `
            display: flex !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background-color: rgba(0,0,0,0.7) !important;
            z-index: 999999 !important;
            justify-content: center !important;
            align-items: center !important;
            visibility: visible !important;
            opacity: 1 !important;
        `;

        const modal = document.createElement('div');
        modal.style.cssText = `
            background: #fff !important;
            padding: 24px !important;
            border-radius: 8px !important;
            min-width: 500px !important;
            max-width: 600px !important;
            max-height: 80vh !important;
            overflow-y: auto !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3) !important;
            visibility: visible !important;
            opacity: 1 !important;
            display: block !important;
            position: relative !important;
            z-index: 1000000 !important;
        `;

        // 使用简单的HTML，避免复杂的模板字符串
        modal.innerHTML = `
            <div style="margin-bottom: 20px;">
                <h2 style="margin: 0; color: #ef4444;">🔥 防火分区配置</h2>
                <p style="margin: 8px 0 0 0; color: #666; font-size: 14px;">
                    配置防火分区的基本信息和安全参数
                </p>
            </div>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 4px; font-weight: 500;">分区名称</label>
                <input type="text" id="zoneName" value="${zoneData.name}" style="
                    width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                    font-size: 14px; box-sizing: border-box;
                ">
            </div>

            <div style="display: flex; gap: 16px; margin-bottom: 20px;">
                <div style="flex: 1;">
                    <label style="display: block; margin-bottom: 4px; font-weight: 500;">防火等级</label>
                    <select id="fireRating" style="
                        width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                        font-size: 14px; box-sizing: border-box;
                    ">
                        <option value="A" ${zoneData.fireRating === 'A' ? 'selected' : ''}>A级 (最高)</option>
                        <option value="B" ${zoneData.fireRating === 'B' ? 'selected' : ''}>B级 (高)</option>
                        <option value="C" ${zoneData.fireRating === 'C' ? 'selected' : ''}>C级 (中)</option>
                        <option value="D" ${zoneData.fireRating === 'D' ? 'selected' : ''}>D级 (低)</option>
                    </select>
                </div>
                <div style="flex: 1;">
                    <label style="display: block; margin-bottom: 4px; font-weight: 500;">最大容量 (人)</label>
                    <input type="number" id="maxOccupancy" value="${zoneData.maxOccupancy}" min="1" style="
                        width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                        font-size: 14px; box-sizing: border-box;
                    ">
                </div>
            </div>

            <div style="display: flex; gap: 16px; margin-bottom: 20px;">
                <div style="flex: 1;">
                    <label style="display: block; margin-bottom: 4px; font-weight: 500;">安全出口数量</label>
                    <input type="number" id="exitCount" value="${zoneData.exitCount}" min="1" style="
                        width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                        font-size: 14px; box-sizing: border-box;
                    ">
                </div>
                <div style="flex: 1;">
                    <label style="display: block; margin-bottom: 4px; font-weight: 500;">疏散距离 (米)</label>
                    <input type="number" id="evacuationDistance" value="30" min="1" style="
                        width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                        font-size: 14px; box-sizing: border-box;
                    ">
                </div>
            </div>

            <div style="margin-bottom: 20px;">
                <h3 style="margin: 0 0 12px 0; font-size: 16px; color: #333;">消防设施</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="checkbox" id="sprinklerSystem" ${zoneData.sprinklerSystem ? 'checked' : ''}>
                        <span>🚿 自动喷淋系统</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="checkbox" id="smokeDetection" ${zoneData.smokeDetection ? 'checked' : ''}>
                        <span>💨 烟雾探测器</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="checkbox" id="fireAlarm">
                        <span>🚨 火灾报警器</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="checkbox" id="fireExtinguisher">
                        <span>🧯 灭火器</span>
                    </label>
                </div>
            </div>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 4px; font-weight: 500;">备注说明</label>
                <textarea id="zoneDescription" rows="3" placeholder="输入防火分区的详细说明..." style="
                    width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
                    font-size: 14px; resize: vertical; box-sizing: border-box;
                ">${zoneData.description || ''}</textarea>
            </div>

            <div style="background: #fef2f2; padding: 16px; border-radius: 6px; border-left: 4px solid #ef4444; margin-bottom: 20px;">
                <h4 style="margin: 0 0 8px 0; font-size: 14px; color: #374151;">📊 区域信息</h4>
                <div style="font-size: 12px; color: #6b7280;">
                    <div>形状: 多边形</div>
                    <div>面积: ${Math.round(zoneData.area)} 平方像素</div>
                    <div>点位数量: ${zoneData.points.length}</div>
                </div>
            </div>

            <div style="text-align: right; display: flex; gap: 12px; justify-content: flex-end;">
                <button id="cancelZoneBtn" style="
                    background: #6b7280; color: white; border: none; padding: 10px 20px;
                    border-radius: 6px; cursor: pointer; font-size: 14px;
                ">取消</button>
                <button id="confirmZoneBtn" style="
                    background: #ef4444; color: white; border: none; padding: 10px 20px;
                    border-radius: 6px; cursor: pointer; font-size: 14px;
                ">确认添加</button>
            </div>
        `;

        overlay.appendChild(modal);

        // 移除现有对话框
        const existingModals = document.querySelectorAll('.fire-zone-config-modal');
        existingModals.forEach(modal => modal.remove());

        console.log('准备添加简化配置对话框到DOM');
        document.body.appendChild(overlay);
        console.log('简化配置对话框已添加到DOM');

        // 创建一个绝对可见的测试元素
        const testElement = document.createElement('div');
        testElement.innerHTML = '🔥 测试对话框 - 如果您能看到这个，说明DOM操作正常！';
        testElement.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            left: 20px !important;
            width: 400px !important;
            height: 100px !important;
            background: yellow !important;
            color: black !important;
            border: 5px solid red !important;
            z-index: 2147483647 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 16px !important;
            font-weight: bold !important;
            text-align: center !important;
        `;
        document.body.appendChild(testElement);

        // 5秒后移除测试元素
        setTimeout(() => {
            if (testElement.parentNode) {
                testElement.parentNode.removeChild(testElement);
            }
        }, 5000);

        // 使用最极端的可见样式 - 绝对能看见！
        overlay.style.display = 'flex !important';
        overlay.style.visibility = 'visible !important';
        overlay.style.opacity = '1 !important';
        overlay.style.zIndex = '2147483647 !important';
        overlay.style.background = 'red !important'; // 纯红色背景
        overlay.style.position = 'fixed !important';
        overlay.style.top = '0 !important';
        overlay.style.left = '0 !important';
        overlay.style.width = '100vw !important';
        overlay.style.height = '100vh !important';

        // 设置正常的对话框样式
        modal.style.background = 'white !important';
        modal.style.border = '1px solid #e2e8f0 !important';
        modal.style.borderRadius = '8px !important';
        modal.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.3) !important';
        modal.style.color = 'black !important';
        modal.style.fontSize = '16px !important';
        modal.style.fontWeight = 'normal !important';
        modal.style.maxWidth = '600px !important';
        modal.style.maxHeight = '80vh !important';
        modal.style.overflowY = 'auto !important';
        modal.style.padding = '20px !important';





        // 绑定事件
        const confirmBtn = overlay.querySelector('#confirmZoneBtn');
        const cancelBtn = overlay.querySelector('#cancelZoneBtn');

        confirmBtn.addEventListener('click', () => {
            const configuredZone = {
                ...zoneData,
                name: overlay.querySelector('#zoneName').value.trim(),
                fireRating: overlay.querySelector('#fireRating').value,
                maxOccupancy: parseInt(overlay.querySelector('#maxOccupancy').value) || 100,
                exitCount: parseInt(overlay.querySelector('#exitCount').value) || 2,
                evacuationDistance: parseInt(overlay.querySelector('#evacuationDistance').value) || 30,
                sprinklerSystem: overlay.querySelector('#sprinklerSystem').checked,
                smokeDetection: overlay.querySelector('#smokeDetection').checked,
                fireAlarm: overlay.querySelector('#fireAlarm').checked,
                fireExtinguisher: overlay.querySelector('#fireExtinguisher').checked,
                description: overlay.querySelector('#zoneDescription').value.trim()
            };

            if (!configuredZone.name) {
                alert('请输入防火分区名称');
                return;
            }

            overlay.remove();
            onConfirm(configuredZone);
        });

        cancelBtn.addEventListener('click', () => {
            overlay.remove();
        });

        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
            }
        });
    }

    /**
     * 绑定防火分区配置事件
     */
    bindZoneConfigEvents(overlay, zoneData, onConfirm) {
        const confirmBtn = overlay.querySelector('#confirmZoneBtn');
        const cancelBtn = overlay.querySelector('#cancelZoneBtn');

        confirmBtn.addEventListener('click', () => {
            const configuredZone = {
                ...zoneData,
                name: overlay.querySelector('#zoneName').value.trim(),
                fireRating: overlay.querySelector('#fireRating').value,
                maxOccupancy: parseInt(overlay.querySelector('#maxOccupancy').value) || 100,
                exitCount: parseInt(overlay.querySelector('#exitCount').value) || 2,
                evacuationDistance: parseInt(overlay.querySelector('#evacuationDistance').value) || 30,
                sprinklerSystem: overlay.querySelector('#sprinklerSystem').checked,
                smokeDetection: overlay.querySelector('#smokeDetection').checked,
                fireAlarm: overlay.querySelector('#fireAlarm').checked,
                fireExtinguisher: overlay.querySelector('#fireExtinguisher').checked,
                emergencyLighting: overlay.querySelector('#emergencyLighting').checked,
                ventilationSystem: overlay.querySelector('#ventilationSystem').checked,
                riskLevel: overlay.querySelector('#riskLevel').value,
                inspectionCycle: parseInt(overlay.querySelector('#inspectionCycle').value) || 30,
                description: overlay.querySelector('#zoneDescription').value.trim()
            };

            if (!configuredZone.name) {
                alert('请输入防火分区名称');
                return;
            }

            overlay.remove();
            onConfirm(configuredZone);
        });

        cancelBtn.addEventListener('click', () => {
            overlay.remove();
        });

        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
            }
        });
    }

    /**
     * 渲染防火分区
     * @param {Object} zone - 防火分区数据
     */
    renderFireZone(zone) {
        const mapViewport = document.getElementById('mapViewport');
        if (!mapViewport) return;

        const zoneElement = document.createElement('div');
        zoneElement.className = 'fire-zone';
        zoneElement.id = `fire-zone-${zone.id}`;

        // 根据形状类型渲染
        if (zone.shape === 'rectangle') {
            this.renderRectangleZone(zoneElement, zone);
        } else if (zone.shape === 'circle') {
            this.renderCircleZone(zoneElement, zone);
        } else if (zone.shape === 'polygon') {
            this.renderPolygonZone(zoneElement, zone);
        }

        mapViewport.appendChild(zoneElement);
    }

    /**
     * 渲染矩形防火分区
     * @param {HTMLElement} element - 元素
     * @param {Object} zone - 分区数据
     */
    renderRectangleZone(element, zone) {
        const [p1, p2] = zone.points;
        const left = Math.min(p1.x, p2.x);
        const top = Math.min(p1.y, p2.y);
        const width = Math.abs(p2.x - p1.x);
        const height = Math.abs(p2.y - p1.y);

        element.style.cssText = `
            position: absolute;
            left: ${left}px;
            top: ${top}px;
            width: ${width}px;
            height: ${height}px;
            border: 2px solid #ef4444;
            background: rgba(239, 68, 68, 0.1);
            pointer-events: auto;
            cursor: pointer;
        `;

        this.addZoneLabel(element, zone);
    }

    /**
     * 渲染圆形防火分区
     * @param {HTMLElement} element - 元素
     * @param {Object} zone - 分区数据
     */
    renderCircleZone(element, zone) {
        const [center, edge] = zone.points;
        const radius = Math.sqrt(
            Math.pow(edge.x - center.x, 2) + Math.pow(edge.y - center.y, 2)
        );

        element.style.cssText = `
            position: absolute;
            left: ${center.x - radius}px;
            top: ${center.y - radius}px;
            width: ${radius * 2}px;
            height: ${radius * 2}px;
            border: 2px solid #ef4444;
            background: rgba(239, 68, 68, 0.1);
            border-radius: 50%;
            pointer-events: auto;
            cursor: pointer;
        `;

        this.addZoneLabel(element, zone);
    }

    /**
     * 渲染多边形防火分区
     * @param {HTMLElement} element - 元素
     * @param {Object} zone - 分区数据
     */
    renderPolygonZone(element, zone) {
        // 计算边界框
        const xs = zone.points.map(p => p.x);
        const ys = zone.points.map(p => p.y);
        const minX = Math.min(...xs);
        const minY = Math.min(...ys);
        const maxX = Math.max(...xs);
        const maxY = Math.max(...ys);

        element.style.cssText = `
            position: absolute;
            left: ${minX}px;
            top: ${minY}px;
            width: ${maxX - minX}px;
            height: ${maxY - minY}px;
            pointer-events: auto;
            cursor: pointer;
            z-index: 50;
        `;

        // 创建SVG多边形
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.style.cssText = 'width: 100%; height: 100%;';

        const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
        const points = zone.points.map(p => `${p.x - minX},${p.y - minY}`).join(' ');
        polygon.setAttribute('points', points);
        polygon.setAttribute('fill', 'rgba(239, 68, 68, 0.1)');
        polygon.setAttribute('stroke', '#ef4444');
        polygon.setAttribute('stroke-width', '2');

        svg.appendChild(polygon);
        element.appendChild(svg);

        this.addZoneLabel(element, zone);
    }

    /**
     * 添加分区标签
     * @param {HTMLElement} element - 元素
     * @param {Object} zone - 分区数据
     */
    addZoneLabel(element, zone) {
        const label = document.createElement('div');
        label.style.cssText = `
            position: absolute;
            top: 4px;
            left: 4px;
            background: rgba(239, 68, 68, 0.9);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            pointer-events: none;
        `;
        label.textContent = zone.name;
        element.appendChild(label);

        // 添加点击事件
        element.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectZone(zone);
        });

        // 添加右键编辑事件
        element.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.showZoneContextMenu(zone, e);
        });
    }

    /**
     * 选择防火分区
     * @param {Object} zone - 分区数据
     */
    selectZone(zone) {
        this.selectedZone = zone;
        showStatusMessage(`已选择防火分区: ${zone.name}`);
        this.highlightZone(zone);
    }

    /**
     * 高亮显示防火分区
     */
    highlightZone(zone) {
        // 清除之前的高亮
        document.querySelectorAll('.fire-zone.highlighted').forEach(el => {
            el.classList.remove('highlighted');
            el.style.boxShadow = '';
        });

        // 高亮当前选中的分区
        const zoneElement = document.getElementById(`fire-zone-${zone.id}`);
        if (zoneElement) {
            zoneElement.classList.add('highlighted');
            zoneElement.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.5)';
        }
    }

    /**
     * 显示防火分区右键菜单
     */
    showZoneContextMenu(zone, e) {
        // 移除现有菜单
        const existingMenu = document.querySelector('.zone-context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }

        const menu = document.createElement('div');
        menu.className = 'zone-context-menu';
        menu.style.cssText = `
            position: fixed;
            left: ${e.clientX}px;
            top: ${e.clientY}px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            min-width: 160px;
            padding: 8px 0;
            font-size: 14px;
        `;

        const menuItems = [
            {
                icon: '✏️',
                text: '编辑分区',
                action: () => this.editZone(zone)
            },
            {
                icon: '📋',
                text: '查看详情',
                action: () => this.showZoneDetails(zone)
            },
            {
                icon: '📊',
                text: '风险评估',
                action: () => this.showRiskAssessment(zone)
            },
            { divider: true },
            {
                icon: '🗑️',
                text: '删除分区',
                action: () => this.deleteZone(zone),
                danger: true
            }
        ];

        menuItems.forEach(item => {
            if (item.divider) {
                const divider = document.createElement('div');
                divider.style.cssText = `
                    height: 1px;
                    background: #e2e8f0;
                    margin: 4px 0;
                `;
                menu.appendChild(divider);
            } else {
                const menuItem = document.createElement('div');
                menuItem.style.cssText = `
                    padding: 8px 16px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    transition: background-color 0.15s ease;
                    ${item.danger ? 'color: #ef4444;' : ''}
                `;

                menuItem.innerHTML = `
                    <span>${item.icon}</span>
                    <span>${item.text}</span>
                `;

                menuItem.addEventListener('mouseenter', () => {
                    menuItem.style.backgroundColor = item.danger ? '#fef2f2' : '#f1f5f9';
                });

                menuItem.addEventListener('mouseleave', () => {
                    menuItem.style.backgroundColor = 'transparent';
                });

                menuItem.addEventListener('click', () => {
                    item.action();
                    menu.remove();
                });

                menu.appendChild(menuItem);
            }
        });

        document.body.appendChild(menu);

        // 调整菜单位置，防止超出屏幕
        const menuRect = menu.getBoundingClientRect();
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        if (menuRect.right > windowWidth) {
            menu.style.left = `${windowWidth - menuRect.width - 10}px`;
        }
        if (menuRect.bottom > windowHeight) {
            menu.style.top = `${windowHeight - menuRect.height - 10}px`;
        }

        // 点击其他地方关闭菜单
        setTimeout(() => {
            document.addEventListener('click', function closeMenu() {
                menu.remove();
                document.removeEventListener('click', closeMenu);
            });
        }, 0);
    }

    /**
     * 编辑防火分区
     */
    editZone(zone) {
        this.showZoneConfigDialog(zone, (updatedZone) => {
            // 更新分区数据
            const index = this.fireZones.findIndex(z => z.id === zone.id);
            if (index !== -1) {
                this.fireZones[index] = updatedZone;
            }

            // 重新渲染分区
            const zoneElement = document.getElementById(`fire-zone-${zone.id}`);
            if (zoneElement) {
                zoneElement.remove();
            }
            this.renderFireZone(updatedZone);

            // 保存更新
            this.saveFireZone(updatedZone);

            showStatusMessage(`防火分区 ${updatedZone.name} 已更新`);
        });
    }

    /**
     * 显示防火分区详情
     */
    showZoneDetails(zone) {
        // TODO: 实现详情查看功能
        showStatusMessage(`查看防火分区详情: ${zone.name}`);
    }

    /**
     * 显示风险评估
     */
    showRiskAssessment(zone) {
        // TODO: 实现风险评估功能
        showStatusMessage(`风险评估: ${zone.name}`);
    }

    /**
     * 删除防火分区
     */
    deleteZone(zone) {
        if (confirm(`确定要删除防火分区 "${zone.name}" 吗？此操作不可恢复。`)) {
            // 从数组中移除
            this.fireZones = this.fireZones.filter(z => z.id !== zone.id);

            // 从DOM中移除
            const zoneElement = document.getElementById(`fire-zone-${zone.id}`);
            if (zoneElement) {
                zoneElement.remove();
            }

            // 从存储中删除
            // TODO: 实现从存储中删除

            showStatusMessage(`已删除防火分区: ${zone.name}`);
        }
    }

    /**
     * 更新预览
     */
    updatePreview(currentPos = null) {
        this.clearPreview();

        const mapViewport = document.getElementById('mapViewport');
        if (!mapViewport || this.currentPoints.length === 0) return;

        // 创建预览元素
        this.previewElement = document.createElement('div');
        this.previewElement.className = 'fire-zone-preview';
        this.previewElement.style.cssText = `
            position: absolute;
            pointer-events: none;
            z-index: 999;
            border: 2px dashed #ef4444;
            background: rgba(239, 68, 68, 0.1);
        `;

        if (this.drawingMode === 'rectangle' && this.currentPoints.length === 1 && currentPos) {
            // 矩形预览
            const startPoint = this.currentPoints[0];
            const left = Math.min(startPoint.x, currentPos.x);
            const top = Math.min(startPoint.y, currentPos.y);
            const width = Math.abs(currentPos.x - startPoint.x);
            const height = Math.abs(currentPos.y - startPoint.y);

            this.previewElement.style.left = `${left}px`;
            this.previewElement.style.top = `${top}px`;
            this.previewElement.style.width = `${width}px`;
            this.previewElement.style.height = `${height}px`;

        } else if (this.drawingMode === 'circle' && this.currentPoints.length === 1 && currentPos) {
            // 圆形预览
            const center = this.currentPoints[0];
            const radius = Math.sqrt(
                Math.pow(currentPos.x - center.x, 2) + Math.pow(currentPos.y - center.y, 2)
            );

            this.previewElement.style.left = `${center.x - radius}px`;
            this.previewElement.style.top = `${center.y - radius}px`;
            this.previewElement.style.width = `${radius * 2}px`;
            this.previewElement.style.height = `${radius * 2}px`;
            this.previewElement.style.borderRadius = '50%';

        } else if (this.drawingMode === 'polygon' && this.currentPoints.length >= 2) {
            // 多边形预览
            const points = [...this.currentPoints];
            if (currentPos) {
                points.push(currentPos);
            }

            // 计算边界框
            const xs = points.map(p => p.x);
            const ys = points.map(p => p.y);
            const minX = Math.min(...xs);
            const minY = Math.min(...ys);
            const maxX = Math.max(...xs);
            const maxY = Math.max(...ys);

            this.previewElement.style.left = `${minX}px`;
            this.previewElement.style.top = `${minY}px`;
            this.previewElement.style.width = `${maxX - minX}px`;
            this.previewElement.style.height = `${maxY - minY}px`;
            this.previewElement.style.border = 'none';
            this.previewElement.style.background = 'none';

            // 创建SVG多边形
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.style.cssText = 'width: 100%; height: 100%;';

            const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            const pointsStr = points.map(p => `${p.x - minX},${p.y - minY}`).join(' ');
            polygon.setAttribute('points', pointsStr);
            polygon.setAttribute('fill', 'rgba(239, 68, 68, 0.1)');
            polygon.setAttribute('stroke', '#ef4444');
            polygon.setAttribute('stroke-width', '2');
            polygon.setAttribute('stroke-dasharray', '5,5');

            svg.appendChild(polygon);
            this.previewElement.appendChild(svg);

            // 添加点位标记
            points.forEach((point) => {
                const pointMarker = document.createElement('div');
                pointMarker.style.cssText = `
                    position: absolute;
                    left: ${point.x - minX - 4}px;
                    top: ${point.y - minY - 4}px;
                    width: 8px;
                    height: 8px;
                    background: #ef4444;
                    border: 2px solid white;
                    border-radius: 50%;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                `;
                this.previewElement.appendChild(pointMarker);
            });
        }

        mapViewport.appendChild(this.previewElement);
    }

    /**
     * 清除预览
     */
    clearPreview() {
        if (this.previewElement && this.previewElement.parentNode) {
            this.previewElement.parentNode.removeChild(this.previewElement);
        }
        this.previewElement = null;
    }

    /**
     * 保存防火分区到存储
     * @param {Object} zone - 分区数据
     */
    async saveFireZone(zone) {
        try {
            if (this.mapController.storage) {
                // 这里可以扩展存储管理器来支持防火分区
                console.log('保存防火分区:', zone);
            }
        } catch (error) {
            console.error('保存防火分区失败:', error);
            showStatusMessage('保存防火分区失败', 3000);
        }
    }
}

export default FireZoneManager;
