/**
 * fire-zone-manager.js - 防火分区管理器
 * 处理防火分区的绘制、编辑和管理
 */

import { showStatusMessage } from './ui-utils.js';

class FireZoneManager {
    constructor(mapController) {
        this.mapController = mapController;
        this.fireZones = [];
        this.isDrawing = false;
        this.currentShape = null;
        this.drawingMode = 'rectangle'; // rectangle, circle, polygon
        this.currentPoints = [];
        this.selectedZone = null;
    }

    /**
     * 开始绘制防火分区
     * @param {Object} startPosition - 起始位置
     */
    startDrawing(startPosition) {
        this.showDrawingModeDialog((mode) => {
            this.drawingMode = mode;
            this.isDrawing = true;
            this.currentPoints = [startPosition];
            
            // 添加绘制事件监听
            this.addDrawingListeners();
            
            showStatusMessage(`开始绘制${this.getShapeName(mode)}防火分区，点击完成绘制`);
        });
    }

    /**
     * 显示绘制模式选择对话框
     * @param {Function} onSelect - 选择回调
     */
    showDrawingModeDialog(onSelect) {
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay drawing-mode-modal';
        overlay.style.cssText = `
            display: flex !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(0,0,0,0.5) !important;
            z-index: 1000 !important;
            justify-content: center !important;
            align-items: center !important;
        `;

        const modal = document.createElement('div');
        modal.style.cssText = `
            background: #fff !important;
            padding: 24px !important;
            border-radius: 8px !important;
            min-width: 400px !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3) !important;
        `;

        modal.innerHTML = `
            <div class="modal-header" style="margin-bottom: 20px;">
                <h2 style="margin: 0; color: #333;">选择防火分区形状</h2>
                <p style="margin: 8px 0 0 0; color: #666; font-size: 14px;">
                    选择要绘制的防火分区形状类型
                </p>
            </div>
            
            <div class="shape-options" style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                <div class="shape-option" data-shape="rectangle" style="
                    padding: 20px; border: 2px solid #e2e8f0; border-radius: 8px; cursor: pointer;
                    text-align: center; transition: all 0.2s ease;
                ">
                    <div style="font-size: 32px; margin-bottom: 8px;">⬜</div>
                    <div style="font-weight: 500;">矩形区域</div>
                    <div style="font-size: 12px; color: #666;">点击两个对角点</div>
                </div>
                
                <div class="shape-option" data-shape="circle" style="
                    padding: 20px; border: 2px solid #e2e8f0; border-radius: 8px; cursor: pointer;
                    text-align: center; transition: all 0.2s ease;
                ">
                    <div style="font-size: 32px; margin-bottom: 8px;">⭕</div>
                    <div style="font-weight: 500;">圆形区域</div>
                    <div style="font-size: 12px; color: #666;">点击中心和边缘</div>
                </div>
                
                <div class="shape-option" data-shape="polygon" style="
                    padding: 20px; border: 2px solid #e2e8f0; border-radius: 8px; cursor: pointer;
                    text-align: center; transition: all 0.2s ease; grid-column: 1 / -1;
                ">
                    <div style="font-size: 32px; margin-bottom: 8px;">🔷</div>
                    <div style="font-weight: 500;">多边形区域</div>
                    <div style="font-size: 12px; color: #666;">点击多个点，双击完成</div>
                </div>
            </div>
            
            <div style="margin-top: 20px; text-align: right;">
                <button id="cancelDrawing" style="
                    background: #6b7280; color: white; border: none; padding: 10px 20px;
                    border-radius: 6px; cursor: pointer; font-size: 14px;
                ">取消</button>
            </div>
        `;

        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // 绑定事件
        modal.querySelectorAll('.shape-option').forEach(option => {
            option.addEventListener('mouseenter', () => {
                option.style.borderColor = '#3b82f6';
                option.style.backgroundColor = '#f8fafc';
            });

            option.addEventListener('mouseleave', () => {
                option.style.borderColor = '#e2e8f0';
                option.style.backgroundColor = 'transparent';
            });

            option.addEventListener('click', () => {
                const shape = option.dataset.shape;
                overlay.remove();
                onSelect(shape);
            });
        });

        modal.querySelector('#cancelDrawing').addEventListener('click', () => {
            overlay.remove();
        });

        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
            }
        });
    }

    /**
     * 添加绘制事件监听
     */
    addDrawingListeners() {
        const mapContainer = document.getElementById('mapContainer');
        if (!mapContainer) return;

        this.drawingClickHandler = (e) => this.handleDrawingClick(e);
        this.drawingMouseMoveHandler = (e) => this.handleDrawingMouseMove(e);
        this.drawingKeyHandler = (e) => this.handleDrawingKey(e);

        mapContainer.addEventListener('click', this.drawingClickHandler);
        mapContainer.addEventListener('mousemove', this.drawingMouseMoveHandler);
        document.addEventListener('keydown', this.drawingKeyHandler);
    }

    /**
     * 移除绘制事件监听
     */
    removeDrawingListeners() {
        const mapContainer = document.getElementById('mapContainer');
        if (!mapContainer) return;

        mapContainer.removeEventListener('click', this.drawingClickHandler);
        mapContainer.removeEventListener('mousemove', this.drawingMouseMoveHandler);
        document.removeEventListener('keydown', this.drawingKeyHandler);
    }

    /**
     * 处理绘制点击事件
     * @param {MouseEvent} e - 鼠标事件
     */
    handleDrawingClick(e) {
        if (!this.isDrawing) return;

        e.stopPropagation();
        
        const rect = e.currentTarget.getBoundingClientRect();
        const position = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };

        if (this.drawingMode === 'rectangle') {
            this.handleRectangleDrawing(position);
        } else if (this.drawingMode === 'circle') {
            this.handleCircleDrawing(position);
        } else if (this.drawingMode === 'polygon') {
            this.handlePolygonDrawing(position);
        }
    }

    /**
     * 处理矩形绘制
     * @param {Object} position - 位置
     */
    handleRectangleDrawing(position) {
        if (this.currentPoints.length === 1) {
            this.currentPoints.push(position);
            this.finishDrawing();
        }
    }

    /**
     * 处理圆形绘制
     * @param {Object} position - 位置
     */
    handleCircleDrawing(position) {
        if (this.currentPoints.length === 1) {
            this.currentPoints.push(position);
            this.finishDrawing();
        }
    }

    /**
     * 处理多边形绘制
     * @param {Object} position - 位置
     */
    handlePolygonDrawing(position) {
        this.currentPoints.push(position);
        this.updatePreview();
    }

    /**
     * 处理绘制鼠标移动
     * @param {MouseEvent} e - 鼠标事件
     */
    handleDrawingMouseMove(e) {
        if (!this.isDrawing) return;
        
        // TODO: 实现实时预览
    }

    /**
     * 处理绘制键盘事件
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleDrawingKey(e) {
        if (!this.isDrawing) return;

        if (e.key === 'Escape') {
            this.cancelDrawing();
        } else if (e.key === 'Enter' && this.drawingMode === 'polygon') {
            this.finishDrawing();
        }
    }

    /**
     * 完成绘制
     */
    finishDrawing() {
        if (this.currentPoints.length < 2) return;

        // 创建防火分区数据
        const zoneData = {
            id: `fire-zone-${Date.now()}`,
            name: '新防火分区',
            shape: this.drawingMode,
            points: this.currentPoints,
            area: this.calculateArea(),
            fireRating: 'A',
            maxOccupancy: 100,
            exitCount: 2,
            sprinklerSystem: true,
            smokeDetection: true,
            description: '',
            createTime: new Date().toISOString()
        };

        // 显示配置对话框
        this.showZoneConfigDialog(zoneData, (configuredZone) => {
            this.fireZones.push(configuredZone);
            this.renderFireZone(configuredZone);
            this.saveFireZone(configuredZone);
            showStatusMessage(`已添加防火分区: ${configuredZone.name}`);
        });

        this.resetDrawing();
    }

    /**
     * 取消绘制
     */
    cancelDrawing() {
        this.resetDrawing();
        showStatusMessage('已取消绘制');
    }

    /**
     * 重置绘制状态
     */
    resetDrawing() {
        this.isDrawing = false;
        this.currentPoints = [];
        this.removeDrawingListeners();
        this.clearPreview();
    }

    /**
     * 计算区域面积
     * @returns {number} 面积
     */
    calculateArea() {
        if (this.drawingMode === 'rectangle' && this.currentPoints.length === 2) {
            const width = Math.abs(this.currentPoints[1].x - this.currentPoints[0].x);
            const height = Math.abs(this.currentPoints[1].y - this.currentPoints[0].y);
            return width * height;
        } else if (this.drawingMode === 'circle' && this.currentPoints.length === 2) {
            const radius = Math.sqrt(
                Math.pow(this.currentPoints[1].x - this.currentPoints[0].x, 2) +
                Math.pow(this.currentPoints[1].y - this.currentPoints[0].y, 2)
            );
            return Math.PI * radius * radius;
        } else if (this.drawingMode === 'polygon') {
            // 使用鞋带公式计算多边形面积
            let area = 0;
            const points = this.currentPoints;
            for (let i = 0; i < points.length; i++) {
                const j = (i + 1) % points.length;
                area += points[i].x * points[j].y;
                area -= points[j].x * points[i].y;
            }
            return Math.abs(area) / 2;
        }
        return 0;
    }

    /**
     * 获取形状名称
     * @param {string} shape - 形状类型
     * @returns {string} 形状名称
     */
    getShapeName(shape) {
        const names = {
            rectangle: '矩形',
            circle: '圆形',
            polygon: '多边形'
        };
        return names[shape] || '';
    }

    /**
     * 显示防火分区配置对话框
     * @param {Object} zoneData - 分区数据
     * @param {Function} onConfirm - 确认回调
     */
    showZoneConfigDialog(zoneData, onConfirm) {
        // TODO: 实现防火分区配置对话框
        // 这里先简化处理，直接确认
        onConfirm(zoneData);
    }

    /**
     * 渲染防火分区
     * @param {Object} zone - 防火分区数据
     */
    renderFireZone(zone) {
        const mapViewport = document.getElementById('mapViewport');
        if (!mapViewport) return;

        const zoneElement = document.createElement('div');
        zoneElement.className = 'fire-zone';
        zoneElement.id = `fire-zone-${zone.id}`;
        
        // 根据形状类型渲染
        if (zone.shape === 'rectangle') {
            this.renderRectangleZone(zoneElement, zone);
        } else if (zone.shape === 'circle') {
            this.renderCircleZone(zoneElement, zone);
        } else if (zone.shape === 'polygon') {
            this.renderPolygonZone(zoneElement, zone);
        }

        mapViewport.appendChild(zoneElement);
    }

    /**
     * 渲染矩形防火分区
     * @param {HTMLElement} element - 元素
     * @param {Object} zone - 分区数据
     */
    renderRectangleZone(element, zone) {
        const [p1, p2] = zone.points;
        const left = Math.min(p1.x, p2.x);
        const top = Math.min(p1.y, p2.y);
        const width = Math.abs(p2.x - p1.x);
        const height = Math.abs(p2.y - p1.y);

        element.style.cssText = `
            position: absolute;
            left: ${left}px;
            top: ${top}px;
            width: ${width}px;
            height: ${height}px;
            border: 2px solid #ef4444;
            background: rgba(239, 68, 68, 0.1);
            pointer-events: auto;
            cursor: pointer;
        `;

        this.addZoneLabel(element, zone);
    }

    /**
     * 渲染圆形防火分区
     * @param {HTMLElement} element - 元素
     * @param {Object} zone - 分区数据
     */
    renderCircleZone(element, zone) {
        const [center, edge] = zone.points;
        const radius = Math.sqrt(
            Math.pow(edge.x - center.x, 2) + Math.pow(edge.y - center.y, 2)
        );

        element.style.cssText = `
            position: absolute;
            left: ${center.x - radius}px;
            top: ${center.y - radius}px;
            width: ${radius * 2}px;
            height: ${radius * 2}px;
            border: 2px solid #ef4444;
            background: rgba(239, 68, 68, 0.1);
            border-radius: 50%;
            pointer-events: auto;
            cursor: pointer;
        `;

        this.addZoneLabel(element, zone);
    }

    /**
     * 渲染多边形防火分区
     * @param {HTMLElement} element - 元素
     * @param {Object} zone - 分区数据
     */
    renderPolygonZone(element, zone) {
        // TODO: 实现多边形渲染
        console.log('渲染多边形防火分区:', zone);
    }

    /**
     * 添加分区标签
     * @param {HTMLElement} element - 元素
     * @param {Object} zone - 分区数据
     */
    addZoneLabel(element, zone) {
        const label = document.createElement('div');
        label.style.cssText = `
            position: absolute;
            top: 4px;
            left: 4px;
            background: rgba(239, 68, 68, 0.9);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            pointer-events: none;
        `;
        label.textContent = zone.name;
        element.appendChild(label);

        // 添加点击事件
        element.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectZone(zone);
        });
    }

    /**
     * 选择防火分区
     * @param {Object} zone - 分区数据
     */
    selectZone(zone) {
        this.selectedZone = zone;
        showStatusMessage(`已选择防火分区: ${zone.name}`);
        // TODO: 实现分区选择高亮效果
    }

    /**
     * 更新预览
     */
    updatePreview() {
        // TODO: 实现实时预览
    }

    /**
     * 清除预览
     */
    clearPreview() {
        // TODO: 实现清除预览
    }

    /**
     * 保存防火分区到存储
     * @param {Object} zone - 分区数据
     */
    async saveFireZone(zone) {
        try {
            if (this.mapController.storage) {
                // 这里可以扩展存储管理器来支持防火分区
                console.log('保存防火分区:', zone);
            }
        } catch (error) {
            console.error('保存防火分区失败:', error);
            showStatusMessage('保存防火分区失败', 3000);
        }
    }
}

export default FireZoneManager;
