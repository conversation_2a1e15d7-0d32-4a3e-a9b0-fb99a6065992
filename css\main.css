/* main.css - 基础样式和变量 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --radius-sm: 6px;
    --radius-md: 8px;
    --transition-fast: all 0.15s ease;
    --transition-smooth: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, sans-serif;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 14px;
    line-height: 1.5;
    overflow: hidden;
}

/* 顶部导航栏 */
.header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: 0 24px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 100;
}

.header h1 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

.header-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.header-btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 8px 16px;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 13px;
    transition: var(--transition-fast);
}

.header-btn:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* 主容器 */
.container {
    display: flex;
    height: calc(100vh - 64px);
}

/* 主内容区 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
    min-width: 0;
}

/* 加载动画 */
.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    z-index: 100;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-spinner-inline {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    color: var(--text-secondary);
    font-size: 14px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 240px;
    }
    
    .map-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .search-container,
    .toolbar-group,
    .zoom-controls {
        width: 100%;
    }

    .monitor-point svg {
        width: 14px;
        height: 14px;
    }

    .legend {
        top: 120px;
        right: 10px;
        left: 10px;
        min-width: auto;
    }

    .bottom-floating-toolbar {
        left: 10px;
        right: 10px;
        bottom: 10px;
        flex-direction: column;
        gap: 12px;
        align-items: center;
    }

    .floating-actions {
        order: 1;
    }

    .floating-version {
        order: 2;
    }

    .login-modal {
        width: 350px;
    }
}